<?php
/**
 * Plugin Name: TunaPix Checkout
 * Plugin URI: https://tunapix.com
 * Description: Plugin de checkout avançado para WordPress com alta conversão, design limpo e personalização extrema por produto.
 * Version: 1.0.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://tunapix.com
 * Text Domain: tunapix-checkout
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevenir acesso direto
if (!defined('ABSPATH')) {
    exit;
}

// Definir constantes do plugin
define('TUNAPIX_VERSION', '1.0.0');
define('TUNAPIX_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TUNAPIX_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TUNAPIX_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Classe principal do plugin
class TunaPixCheckout {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Carregar classes principais
        $this->load_dependencies();
        
        // Inicializar componentes
        $this->init_components();
    }
    
    private function load_dependencies() {
        // Carregar classes principais
        require_once TUNAPIX_PLUGIN_PATH . 'includes/class-tunapix-admin.php';
        require_once TUNAPIX_PLUGIN_PATH . 'includes/class-tunapix-checkout-page.php';
        require_once TUNAPIX_PLUGIN_PATH . 'includes/class-tunapix-products.php';
        require_once TUNAPIX_PLUGIN_PATH . 'includes/class-tunapix-payments.php';
        require_once TUNAPIX_PLUGIN_PATH . 'includes/class-tunapix-analytics.php';
        require_once TUNAPIX_PLUGIN_PATH . 'includes/class-tunapix-facebook-pixel.php';
        require_once TUNAPIX_PLUGIN_PATH . 'includes/class-tunapix-webhooks.php';
        require_once TUNAPIX_PLUGIN_PATH . 'includes/class-tunapix-settings.php';
    }
    
    private function init_components() {
        try {
            // Inicializar componentes
            TunaPix_Admin::get_instance();
            TunaPix_Checkout_Page::get_instance();
            TunaPix_Products::get_instance();
            TunaPix_Payments::get_instance();
            TunaPix_Analytics::get_instance();
            TunaPix_Facebook_Pixel::get_instance();
            TunaPix_Webhooks::get_instance();
            TunaPix_Settings::get_instance();
        } catch (Exception $e) {
            // Log do erro para debug
            error_log('TunaPix Checkout Error: ' . $e->getMessage());
        }
    }
    
    public function load_textdomain() {
        load_plugin_textdomain('tunapix-checkout', false, dirname(TUNAPIX_PLUGIN_BASENAME) . '/languages');
    }
    
    public function activate() {
        // Criar tabelas do banco de dados
        $this->create_tables();
        
        // Definir opções padrão
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        flush_rewrite_rules();
    }
    
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Tabela de produtos
        $table_products = $wpdb->prefix . 'tunapix_products';
        $sql_products = "CREATE TABLE $table_products (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            price decimal(10,2) NOT NULL,
            currency varchar(3) DEFAULT 'BRL',
            checkout_banner varchar(255),
            social_proof text,
            delivery_email text,
            order_bump_enabled tinyint(1) DEFAULT 0,
            order_bump_name varchar(255),
            order_bump_price decimal(10,2),
            order_bump_description text,
            order_bump_delivery_email text,
            theme varchar(10) DEFAULT 'white',
            custom_colors text,
            facebook_pixels text,
            redirect_url varchar(255),
            fields_enabled text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        // Tabela de pedidos
        $table_orders = $wpdb->prefix . 'tunapix_orders';
        $sql_orders = "CREATE TABLE $table_orders (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id mediumint(9) NOT NULL,
            customer_name varchar(255) NOT NULL,
            customer_email varchar(255) NOT NULL,
            customer_cpf varchar(20),
            customer_phone varchar(20),
            payment_method varchar(20) NOT NULL,
            payment_status varchar(20) DEFAULT 'pending',
            payment_gateway varchar(50),
            gateway_transaction_id varchar(255),
            amount decimal(10,2) NOT NULL,
            currency varchar(3) DEFAULT 'BRL',
            order_bump_purchased tinyint(1) DEFAULT 0,
            order_bump_amount decimal(10,2),
            ip_address varchar(45),
            user_agent text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id),
            KEY payment_status (payment_status),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_products);
        dbDelta($sql_orders);
    }
    
    private function set_default_options() {
        $default_options = array(
            'tunapix_currency' => 'BRL',
            'tunapix_country' => 'BR',
            'tunapix_theme' => 'white',
            'tunapix_facebook_pixels' => '',
            'tunapix_webhook_url' => '',
            'tunapix_fields_enabled' => array(
                'name' => 1,
                'cpf' => 1,
                'phone' => 1
            ),
            'tunapix_payment_gateways' => array(
                'mercadopago' => 1,
                'stripe' => 0,
                'asaas' => 0,
                'pagseguro' => 0,
                'pushpay' => 0,
                'cashtime' => 0
            )
        );
        
        foreach ($default_options as $key => $value) {
            if (get_option($key) === false) {
                update_option($key, $value);
            }
        }
    }
}

// Inicializar o plugin
function tunapix_checkout() {
    return TunaPixCheckout::get_instance();
}

// Iniciar o plugin
tunapix_checkout(); 