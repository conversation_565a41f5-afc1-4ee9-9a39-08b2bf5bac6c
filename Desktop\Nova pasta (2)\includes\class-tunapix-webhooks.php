<?php
/**
 * Classe de Webhooks
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Webhooks {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Construtor vazio
    }
    
    public function send_webhook($order_id, $event_type) {
        $webhook_url = get_option('tunapix_webhook_url', '');
        
        if (empty($webhook_url)) {
            return false;
        }
        
        global $wpdb;
        
        // Buscar dados do pedido
        $order = $wpdb->get_row($wpdb->prepare(
            "SELECT o.*, p.name as product_name, p.description as product_description 
             FROM {$wpdb->prefix}tunapix_orders o 
             JOIN {$wpdb->prefix}tunapix_products p ON o.product_id = p.id 
             WHERE o.id = %d",
            $order_id
        ));
        
        if (!$order) {
            return false;
        }
        
        // Preparar dados do webhook
        $webhook_data = array(
            'event_type' => $event_type,
            'order_id' => $order_id,
            'timestamp' => current_time('timestamp'),
            'order' => array(
                'id' => $order->id,
                'product_id' => $order->product_id,
                'product_name' => $order->product_name,
                'product_description' => $order->product_description,
                'customer_name' => $order->customer_name,
                'customer_email' => $order->customer_email,
                'customer_cpf' => $order->customer_cpf,
                'customer_phone' => $order->customer_phone,
                'payment_method' => $order->payment_method,
                'payment_status' => $order->payment_status,
                'payment_gateway' => $order->payment_gateway,
                'gateway_transaction_id' => $order->gateway_transaction_id,
                'amount' => $order->amount,
                'currency' => $order->currency,
                'order_bump_purchased' => $order->order_bump_purchased,
                'order_bump_amount' => $order->order_bump_amount,
                'ip_address' => $order->ip_address,
                'user_agent' => $order->user_agent,
                'created_at' => $order->created_at,
                'updated_at' => $order->updated_at
            ),
            'site' => array(
                'url' => home_url(),
                'name' => get_bloginfo('name'),
                'email' => get_option('admin_email')
            )
        );
        
        // Enviar webhook
        $response = wp_remote_post($webhook_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'X-TunaPix-Event' => $event_type,
                'X-TunaPix-Signature' => $this->generate_signature($webhook_data)
            ),
            'body' => json_encode($webhook_data),
            'timeout' => 30,
            'blocking' => false // Enviar de forma assíncrona
        ));
        
        if (is_wp_error($response)) {
            error_log('TunaPix: Erro ao enviar webhook: ' . $response->get_error_message());
            return false;
        }
        
        return true;
    }
    
    private function generate_signature($data) {
        $secret = get_option('tunapix_webhook_secret', '');
        
        if (empty($secret)) {
            return '';
        }
        
        return hash_hmac('sha256', json_encode($data), $secret);
    }
    
    public function test_webhook() {
        $webhook_url = get_option('tunapix_webhook_url', '');
        
        if (empty($webhook_url)) {
            return array('success' => false, 'message' => 'URL do webhook não configurada');
        }
        
        $test_data = array(
            'event_type' => 'test',
            'timestamp' => current_time('timestamp'),
            'message' => 'Teste de webhook do TunaPix Checkout',
            'site' => array(
                'url' => home_url(),
                'name' => get_bloginfo('name')
            )
        );
        
        $response = wp_remote_post($webhook_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'X-TunaPix-Event' => 'test',
                'X-TunaPix-Signature' => $this->generate_signature($test_data)
            ),
            'body' => json_encode($test_data),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => 'Erro ao enviar webhook: ' . $response->get_error_message());
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code >= 200 && $response_code < 300) {
            return array('success' => true, 'message' => 'Webhook enviado com sucesso!');
        } else {
            return array('success' => false, 'message' => 'Erro no servidor: ' . $response_code);
        }
    }
    
    public function get_webhook_events() {
        return array(
            'purchase' => 'Compra aprovada',
            'refund' => 'Reembolso',
            'cancelled' => 'Pedido cancelado',
            'pending' => 'Pagamento pendente',
            'failed' => 'Pagamento falhou'
        );
    }
    
    public function log_webhook($order_id, $event_type, $response) {
        global $wpdb;
        
        $log_data = array(
            'order_id' => $order_id,
            'event_type' => $event_type,
            'webhook_url' => get_option('tunapix_webhook_url', ''),
            'response_code' => is_wp_error($response) ? 0 : wp_remote_retrieve_response_code($response),
            'response_body' => is_wp_error($response) ? $response->get_error_message() : wp_remote_retrieve_body($response),
            'created_at' => current_time('mysql')
        );
        
        $wpdb->insert(
            $wpdb->prefix . 'tunapix_webhook_logs',
            $log_data,
            array('%d', '%s', '%s', '%d', '%s', '%s')
        );
    }
} 