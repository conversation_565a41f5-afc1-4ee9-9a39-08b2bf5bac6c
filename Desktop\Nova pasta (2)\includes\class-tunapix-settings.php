<?php
/**
 * Classe de Configurações
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Settings {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_init', array($this, 'register_settings'));
        add_action('wp_ajax_tunapix_test_webhook', array($this, 'test_webhook'));
    }
    
    public function register_settings() {
        // Configurações gerais
        register_setting('tunapix_settings', 'tunapix_currency');
        register_setting('tunapix_settings', 'tunapix_country');
        register_setting('tunapix_settings', 'tunapix_theme');
        register_setting('tunapix_settings', 'tunapix_facebook_pixels');
        register_setting('tunapix_settings', 'tunapix_facebook_access_token');
        register_setting('tunapix_settings', 'tunapix_webhook_url');
        register_setting('tunapix_settings', 'tunapix_webhook_secret');
        register_setting('tunapix_settings', 'tunapix_fields_enabled');
        register_setting('tunapix_settings', 'tunapix_payment_gateways');
        
        // Configurações de gateways
        register_setting('tunapix_settings', 'tunapix_mercadopago_access_token');
        register_setting('tunapix_settings', 'tunapix_mercadopago_public_key');
        register_setting('tunapix_settings', 'tunapix_stripe_secret_key');
        register_setting('tunapix_settings', 'tunapix_stripe_publishable_key');
        register_setting('tunapix_settings', 'tunapix_asaas_api_key');
        register_setting('tunapix_settings', 'tunapix_pagseguro_email');
        register_setting('tunapix_settings', 'tunapix_pagseguro_token');
        register_setting('tunapix_settings', 'tunapix_pushpay_merchant_id');
        register_setting('tunapix_settings', 'tunapix_pushpay_secret_key');
        register_setting('tunapix_settings', 'tunapix_cashtime_api_key');
    }
    
    public function test_webhook() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }
        
        $result = TunaPix_Webhooks::get_instance()->test_webhook();
        wp_send_json($result);
    }
    
    public function get_currencies() {
        return array(
            'BRL' => array(
                'name' => 'Real Brasileiro',
                'symbol' => 'R$',
                'position' => 'left'
            ),
            'USD' => array(
                'name' => 'Dólar Americano',
                'symbol' => '$',
                'position' => 'left'
            ),
            'EUR' => array(
                'name' => 'Euro',
                'symbol' => '€',
                'position' => 'left'
            ),
            'GBP' => array(
                'name' => 'Libra Esterlina',
                'symbol' => '£',
                'position' => 'left'
            ),
            'CAD' => array(
                'name' => 'Dólar Canadense',
                'symbol' => 'C$',
                'position' => 'left'
            ),
            'AUD' => array(
                'name' => 'Dólar Australiano',
                'symbol' => 'A$',
                'position' => 'left'
            )
        );
    }
    
    public function get_countries() {
        return array(
            'BR' => 'Brasil',
            'US' => 'Estados Unidos',
            'CA' => 'Canadá',
            'GB' => 'Reino Unido',
            'DE' => 'Alemanha',
            'FR' => 'França',
            'IT' => 'Itália',
            'ES' => 'Espanha',
            'PT' => 'Portugal',
            'AU' => 'Austrália',
            'MX' => 'México',
            'AR' => 'Argentina',
            'CL' => 'Chile',
            'CO' => 'Colômbia',
            'PE' => 'Peru',
            'UY' => 'Uruguai',
            'PY' => 'Paraguai',
            'BO' => 'Bolívia',
            'EC' => 'Equador',
            'VE' => 'Venezuela'
        );
    }
    
    public function get_themes() {
        return array(
            'white' => array(
                'name' => 'Tema Claro',
                'description' => 'Design limpo com fundo branco',
                'preview' => 'white-theme.png'
            ),
            'dark' => array(
                'name' => 'Tema Escuro',
                'description' => 'Design moderno com fundo escuro',
                'preview' => 'dark-theme.png'
            )
        );
    }
    
    public function get_payment_gateways() {
        return array(
            'mercadopago' => array(
                'name' => 'Mercado Pago',
                'description' => 'Aceita cartões, PIX, boleto e transferência',
                'icon' => 'mercadopago-icon.png',
                'supported_currencies' => array('BRL', 'ARS', 'CLP', 'COP', 'MXN', 'PEN', 'UYU'),
                'settings' => array(
                    'access_token' => array(
                        'label' => 'Access Token',
                        'type' => 'text',
                        'required' => true,
                        'description' => 'Token de acesso do Mercado Pago'
                    ),
                    'public_key' => array(
                        'label' => 'Public Key',
                        'type' => 'text',
                        'required' => true,
                        'description' => 'Chave pública do Mercado Pago'
                    )
                )
            ),
            'stripe' => array(
                'name' => 'Stripe',
                'description' => 'Cartões de crédito e débito internacionais',
                'icon' => 'stripe-icon.png',
                'supported_currencies' => array('USD', 'EUR', 'GBP', 'CAD', 'AUD'),
                'settings' => array(
                    'secret_key' => array(
                        'label' => 'Secret Key',
                        'type' => 'text',
                        'required' => true,
                        'description' => 'Chave secreta do Stripe'
                    ),
                    'publishable_key' => array(
                        'label' => 'Publishable Key',
                        'type' => 'text',
                        'required' => true,
                        'description' => 'Chave pública do Stripe'
                    )
                )
            ),
            'asaas' => array(
                'name' => 'Asaas',
                'description' => 'PIX, boleto e cartão de crédito',
                'icon' => 'asaas-icon.png',
                'supported_currencies' => array('BRL'),
                'settings' => array(
                    'api_key' => array(
                        'label' => 'API Key',
                        'type' => 'text',
                        'required' => true,
                        'description' => 'Chave da API do Asaas'
                    )
                )
            ),
            'pagseguro' => array(
                'name' => 'PagSeguro',
                'description' => 'Múltiplas formas de pagamento',
                'icon' => 'pagseguro-icon.png',
                'supported_currencies' => array('BRL'),
                'settings' => array(
                    'email' => array(
                        'label' => 'E-mail',
                        'type' => 'email',
                        'required' => true,
                        'description' => 'E-mail da conta PagSeguro'
                    ),
                    'token' => array(
                        'label' => 'Token',
                        'type' => 'text',
                        'required' => true,
                        'description' => 'Token de segurança do PagSeguro'
                    )
                )
            ),
            'pushpay' => array(
                'name' => 'PushPay',
                'description' => 'Pagamentos móveis e online',
                'icon' => 'pushpay-icon.png',
                'supported_currencies' => array('USD', 'NZD', 'AUD'),
                'settings' => array(
                    'merchant_id' => array(
                        'label' => 'Merchant ID',
                        'type' => 'text',
                        'required' => true,
                        'description' => 'ID do comerciante PushPay'
                    ),
                    'secret_key' => array(
                        'label' => 'Secret Key',
                        'type' => 'text',
                        'required' => true,
                        'description' => 'Chave secreta do PushPay'
                    )
                )
            ),
            'cashtime' => array(
                'name' => 'CashTime',
                'description' => 'Pagamentos instantâneos',
                'icon' => 'cashtime-icon.png',
                'supported_currencies' => array('BRL'),
                'settings' => array(
                    'api_key' => array(
                        'label' => 'API Key',
                        'type' => 'text',
                        'required' => true,
                        'description' => 'Chave da API do CashTime'
                    )
                )
            )
        );
    }
    
    public function get_form_fields() {
        return array(
            'name' => array(
                'label' => 'Nome Completo',
                'required' => true,
                'type' => 'text'
            ),
            'email' => array(
                'label' => 'E-mail',
                'required' => true,
                'type' => 'email'
            ),
            'cpf' => array(
                'label' => 'CPF',
                'required' => false,
                'type' => 'text'
            ),
            'phone' => array(
                'label' => 'Telefone',
                'required' => false,
                'type' => 'tel'
            )
        );
    }
    
    public function validate_settings($settings) {
        $errors = array();
        
        // Validar moeda
        $currencies = $this->get_currencies();
        if (!isset($currencies[$settings['currency']])) {
            $errors[] = 'Moeda inválida';
        }
        
        // Validar país
        $countries = $this->get_countries();
        if (!isset($countries[$settings['country']])) {
            $errors[] = 'País inválido';
        }
        
        // Validar tema
        $themes = $this->get_themes();
        if (!isset($themes[$settings['theme']])) {
            $errors[] = 'Tema inválido';
        }
        
        // Validar pixels do Facebook
        if (!empty($settings['facebook_pixels'])) {
            $pixels = explode("\n", $settings['facebook_pixels']);
            foreach ($pixels as $pixel) {
                $pixel = trim($pixel);
                if (!empty($pixel) && !is_numeric($pixel)) {
                    $errors[] = 'ID do Pixel do Facebook deve ser numérico';
                    break;
                }
            }
        }
        
        // Validar URL do webhook
        if (!empty($settings['webhook_url']) && !filter_var($settings['webhook_url'], FILTER_VALIDATE_URL)) {
            $errors[] = 'URL do webhook inválida';
        }
        
        return $errors;
    }
} 