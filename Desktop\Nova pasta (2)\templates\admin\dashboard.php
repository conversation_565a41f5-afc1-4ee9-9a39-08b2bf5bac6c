<?php
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap tunapix-admin">
    <h1>TunaPix Checkout - Dashboard</h1>
    
    <!-- Filtros de Período -->
    <div class="tunapix-filters">
        <select id="period-filter">
            <option value="7">Últimos 7 dias</option>
            <option value="30">Últimos 30 dias</option>
            <option value="90">Últimos 90 dias</option>
        </select>
        <button id="refresh-analytics" class="button">Atualizar</button>
    </div>
    
    <!-- Métricas Principais -->
    <div class="tunapix-metrics">
        <div class="metric-card">
            <div class="metric-icon">📊</div>
            <div class="metric-content">
                <h3>Total de Pedidos</h3>
                <div class="metric-value" id="total-orders">0</div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-icon">✅</div>
            <div class="metric-content">
                <h3>Pedidos Pagos</h3>
                <div class="metric-value" id="paid-orders">0</div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-icon">💰</div>
            <div class="metric-content">
                <h3>Faturamento</h3>
                <div class="metric-value" id="total-revenue">R$ 0,00</div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-icon">📈</div>
            <div class="metric-content">
                <h3>Ticket Médio</h3>
                <div class="metric-value" id="average-ticket">R$ 0,00</div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-icon">🔄</div>
            <div class="metric-content">
                <h3>Taxa de Conversão</h3>
                <div class="metric-value" id="conversion-rate">0%</div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-icon">❌</div>
            <div class="metric-content">
                <h3>Reembolsos</h3>
                <div class="metric-value" id="refunded-orders">0</div>
            </div>
        </div>
    </div>
    
    <!-- Gráficos -->
    <div class="tunapix-charts">
        <div class="chart-container">
            <h3>Vendas por Dia</h3>
            <canvas id="sales-chart"></canvas>
        </div>
        
        <div class="chart-container">
            <h3>Produtos Mais Vendidos</h3>
            <div id="top-products"></div>
        </div>
    </div>
    
    <!-- Pedidos Recentes -->
    <div class="tunapix-recent-orders">
        <h3>Pedidos Recentes</h3>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Cliente</th>
                    <th>Produto</th>
                    <th>Valor</th>
                    <th>Status</th>
                    <th>Data</th>
                </tr>
            </thead>
            <tbody id="recent-orders-list">
                <!-- Preenchido via JavaScript -->
            </tbody>
        </table>
    </div>
</div>

<style>
.tunapix-admin {
    margin: 20px;
}

.tunapix-filters {
    margin-bottom: 30px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tunapix-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.metric-icon {
    font-size: 2em;
    margin-right: 15px;
}

.metric-content h3 {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 14px;
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.tunapix-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-container h3 {
    margin-top: 0;
    margin-bottom: 20px;
}

.tunapix-recent-orders {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tunapix-recent-orders h3 {
    margin-top: 0;
    margin-bottom: 20px;
}

.status-completed {
    color: #28a745;
    font-weight: bold;
}

.status-pending {
    color: #ffc107;
    font-weight: bold;
}

.status-failed {
    color: #dc3545;
    font-weight: bold;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Carregar analytics iniciais
    loadAnalytics();
    
    // Event listeners
    $('#refresh-analytics').on('click', loadAnalytics);
    $('#period-filter').on('change', loadAnalytics);
    
    function loadAnalytics() {
        const period = $('#period-filter').val();
        
        $.ajax({
            url: tunapix_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'tunapix_get_analytics',
                nonce: tunapix_ajax.nonce,
                period: period
            },
            success: function(response) {
                if (response.success) {
                    updateMetrics(response.data);
                    updateCharts(response.data);
                    loadRecentOrders();
                }
            },
            error: function() {
                alert('Erro ao carregar analytics');
            }
        });
    }
    
    function updateMetrics(data) {
        $('#total-orders').text(data.total_orders);
        $('#paid-orders').text(data.paid_orders);
        $('#total-revenue').text('R$ ' + data.total_revenue.toFixed(2).replace('.', ','));
        $('#average-ticket').text('R$ ' + data.average_ticket.toFixed(2).replace('.', ','));
        $('#conversion-rate').text(data.conversion_rate.toFixed(1) + '%');
        $('#refunded-orders').text(data.refunded_orders);
    }
    
    function updateCharts(data) {
        // Gráfico de vendas por dia
        if (data.daily_sales && data.daily_sales.length > 0) {
            const ctx = document.getElementById('sales-chart').getContext('2d');
            
            if (window.salesChart) {
                window.salesChart.destroy();
            }
            
            window.salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.daily_sales.map(item => item.date),
                    datasets: [{
                        label: 'Vendas (R$)',
                        data: data.daily_sales.map(item => item.revenue),
                        borderColor: '#0073aa',
                        backgroundColor: 'rgba(0, 115, 170, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        // Produtos mais vendidos
        if (data.top_products && data.top_products.length > 0) {
            let html = '<ul>';
            data.top_products.forEach(product => {
                html += `<li><strong>${product.name}</strong> - ${product.sales} vendas (R$ ${product.revenue.toFixed(2).replace('.', ',')})</li>`;
            });
            html += '</ul>';
            $('#top-products').html(html);
        }
    }
    
    function loadRecentOrders() {
        $.ajax({
            url: tunapix_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'tunapix_get_recent_orders',
                nonce: tunapix_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateRecentOrdersTable(response.data);
                }
            }
        });
    }
    
    function updateRecentOrdersTable(orders) {
        let html = '';
        
        orders.forEach(order => {
            const statusClass = 'status-' + order.payment_status;
            const statusText = {
                'completed': 'Aprovado',
                'pending': 'Pendente',
                'failed': 'Falhou',
                'cancelled': 'Cancelado',
                'refunded': 'Reembolsado'
            };
            
            html += `
                <tr>
                    <td>#${order.id}</td>
                    <td>${order.customer_name}<br><small>${order.customer_email}</small></td>
                    <td>${order.product_name}</td>
                    <td>R$ ${parseFloat(order.amount).toFixed(2).replace('.', ',')}</td>
                    <td><span class="${statusClass}">${statusText[order.payment_status] || order.payment_status}</span></td>
                    <td>${new Date(order.created_at).toLocaleDateString('pt-BR')}</td>
                </tr>
            `;
        });
        
        $('#recent-orders-list').html(html);
    }
});
</script> 