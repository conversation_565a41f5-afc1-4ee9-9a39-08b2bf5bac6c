<?php
/**
 * Gateway de Pagamento - Asaas
 * 
 * @package TunaPix_Checkout
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Asaas {
    
    private $api_key;
    private $api_url = 'https://api.asaas.com/v3';
    
    public function __construct() {
        $this->api_key = get_option('tunapix_asaas_api_key', '');
    }
    
    /**
     * Processa pagamento via Asaas
     */
    public function process_payment($order_id, $product, $customer_data) {
        // Implementação futura do Asaas
        return array(
            'success' => false,
            'message' => 'Gateway Asaas ainda não implementado'
        );
    }
    
    /**
     * Processa webhook do Asaas
     */
    public function process_webhook($data) {
        // Implementação futura do webhook Asaas
        return array(
            'success' => false,
            'message' => 'Webhook Asaas ainda não implementado'
        );
    }
    
    /**
     * Retorna métodos de pagamento disponíveis
     */
    public function get_payment_methods() {
        return array(
            'card' => 'Cartão de Crédito/Débito',
            'pix' => 'PIX',
            'boleto' => 'Boleto Bancário'
        );
    }
    
    /**
     * Verifica se o gateway está disponível
     */
    public function is_available() {
        return !empty($this->api_key);
    }
    
    /**
     * Mapeia status do Asaas para status do plugin
     */
    private function map_payment_status($asaas_status) {
        $status_map = array(
            'RECEIVED' => 'completed',
            'CONFIRMED' => 'completed',
            'OVERDUE' => 'pending',
            'DUE' => 'pending',
            'CANCELLED' => 'cancelled',
            'REFUNDED' => 'refunded'
        );
        
        return isset($status_map[$asaas_status]) ? $status_map[$asaas_status] : 'pending';
    }
} 