# TunaPix Checkout - Plugin WordPress

Um plugin avançado de checkout para WordPress com alta conversão, design limpo e personalização extrema por produto.

## 🚀 Características Principais

### ✨ Design e Interface
- **Layout de Página Única**: Checkout completo em uma única página
- **Temas Personalizáveis**: Suporte a tema claro (White) e escuro (Dark)
- **Design Responsivo**: Funciona perfeitamente em desktop e mobile
- **Personalização de Cores**: Controle total sobre cores de fundo, botões e textos
- **Banner com Timer**: Contador regressivo para criar urgência

### 💳 Métodos de Pagamento
- **Mercado Pago**: Cartões, PIX, boleto e transferência
- **Stripe**: Cartões de crédito e débito internacionais
- **Asaas**: PIX, boleto e cartão de crédito
- **PagSeguro**: Múltiplas formas de pagamento
- **PushPay**: Pagamentos móveis
- **CashTime**: Pagamentos instantâneos

### 📊 Analytics e Relatórios
- **Dashboard Completo**: Métricas em tempo real
- **Filtros por Período**: 7, 30 e 90 dias
- **Métricas Principais**: Total de pedidos, faturamento, ticket médio
- **Gráficos Interativos**: Vendas por dia e produtos mais vendidos
- **Pedidos Recentes**: Lista atualizada de transações

### 🎯 Funcionalidades Avançadas
- **Order Bump**: Ofertas complementares configuráveis
- **Controle de Campos**: Habilitar/desabilitar campos do formulário
- **E-mail Personalizado**: Templates de entrega por produto
- **Webhooks**: Integração com sistemas externos
- **Facebook Pixel**: Rastreamento automático de eventos

### 🌍 Internacionalização
- **Múltiplas Moedas**: BRL, USD, EUR, GBP, CAD, AUD
- **Múltiplos Países**: Suporte a diversos mercados
- **Idioma Português**: Interface totalmente em português

## 📋 Requisitos

- WordPress 5.0 ou superior
- PHP 7.4 ou superior
- MySQL 5.6 ou superior
- SSL ativo (recomendado para pagamentos)

## 🔧 Instalação

### 1. Upload do Plugin
1. Faça o download do plugin
2. Acesse o painel administrativo do WordPress
3. Vá em **Plugins > Adicionar Novo > Enviar Plugin**
4. Selecione o arquivo ZIP do TunaPix Checkout
5. Clique em **Instalar Agora**

### 2. Ativação
1. Após a instalação, clique em **Ativar Plugin**
2. O plugin criará automaticamente as tabelas necessárias
3. Acesse o menu **TunaPix** no painel administrativo

### 3. Configuração Inicial
1. Vá em **TunaPix > Configurações**
2. Configure a moeda e país da sua loja
3. Ative os gateways de pagamento desejados
4. Configure as credenciais dos gateways
5. Adicione seus Pixels do Facebook

## 🛠️ Configuração

### Configurações Gerais

#### Moeda e Localização
- **Moeda**: Selecione a moeda principal da sua loja
- **País**: Defina o país de operação
- **Tema**: Escolha entre tema claro ou escuro

#### Facebook Pixel
- **IDs do Pixel**: Insira um ID por linha
- **Access Token**: Token para eventos server-side (opcional)

#### Webhooks
- **URL do Webhook**: Endpoint para receber notificações
- **Secret**: Chave secreta para validação

### Gateways de Pagamento

#### Mercado Pago
1. Acesse [Mercado Pago Developers](https://developers.mercadopago.com)
2. Crie uma conta e obtenha suas credenciais
3. Configure o Access Token e Public Key no plugin

#### Stripe
1. Crie uma conta em [Stripe](https://stripe.com)
2. Obtenha as chaves de API (Secret e Publishable)
3. Configure as chaves no plugin

#### Outros Gateways
Siga as instruções específicas de cada gateway no painel de configurações.

## 📦 Criação de Produtos

### 1. Informações Básicas
- **Nome do Produto**: Título que aparecerá no checkout
- **Descrição**: Descrição detalhada do produto
- **Preço**: Valor em centavos (ex: 3900 = R$ 39,00)
- **Moeda**: Moeda do produto

### 2. Personalização Visual
- **Banner do Checkout**: Imagem principal do produto
- **Provas Sociais**: Depoimentos e social proof
- **Tema**: Tema específico para este produto
- **Cores Personalizadas**: Cores únicas para este produto

### 3. Order Bump
- **Ativar Order Bump**: Habilitar oferta complementar
- **Nome do Bônus**: Título da oferta adicional
- **Preço do Bônus**: Valor do produto adicional
- **Descrição**: Detalhes do bônus

### 4. Configurações Avançadas
- **Campos Habilitados**: Controle quais campos aparecem
- **Pixels do Facebook**: Pixels específicos para este produto
- **URL de Redirecionamento**: Página após a compra
- **E-mail de Entrega**: Template personalizado

## 🎨 Personalização

### Temas
O plugin suporta dois temas principais:

#### Tema Claro (White)
- Fundo branco
- Texto escuro
- Cores padrão do WordPress

#### Tema Escuro (Dark)
- Fundo escuro (#1a1a1a)
- Texto claro
- Campos com fundo escuro

### Cores Personalizadas
Para cada produto, você pode definir:
- **Cor Primária**: Cor principal dos botões
- **Cor Secundária**: Cor dos botões secundários
- **Cor do Texto**: Cor principal do texto

### CSS Customizado
Adicione CSS personalizado no painel de configurações para modificações específicas.

## 📊 Analytics

### Dashboard Principal
- **Total de Pedidos**: Número total de pedidos no período
- **Pedidos Pagos**: Pedidos com pagamento aprovado
- **Faturamento**: Valor total das vendas
- **Ticket Médio**: Valor médio por pedido
- **Taxa de Conversão**: Percentual de conversão
- **Reembolsos**: Pedidos reembolsados

### Gráficos
- **Vendas por Dia**: Gráfico de linha com vendas diárias
- **Produtos Mais Vendidos**: Ranking dos produtos
- **Métodos de Pagamento**: Distribuição por gateway

### Filtros
- **Período**: 7, 30 ou 90 dias
- **Status**: Aprovado, pendente, falhou
- **Gateway**: Filtrar por método de pagamento

## 🔗 Integrações

### Facebook Pixel
O plugin dispara automaticamente os seguintes eventos:
- **PageView**: Visualização da página
- **InitiateCheckout**: Início do checkout
- **Purchase**: Compra finalizada

### Webhooks
Envia notificações para sistemas externos com:
- Dados completos do pedido
- Informações do cliente
- Status do pagamento
- Timestamp da transação

### E-mail Marketing
- Integração com principais plataformas
- Tags personalizadas para automação
- Segmentação por produto

## 🛡️ Segurança

### Proteções Implementadas
- **Validação de Dados**: Sanitização de todos os inputs
- **Nonces**: Proteção contra CSRF
- **Capabilities**: Controle de acesso por usuário
- **HTTPS**: Suporte obrigatório para pagamentos
- **Logs**: Registro de todas as transações

### Boas Práticas
- Mantenha o WordPress atualizado
- Use plugins de segurança
- Configure backups regulares
- Monitore logs de acesso

## 🐛 Solução de Problemas

### Problemas Comuns

#### Plugin não aparece no menu
- Verifique se o plugin está ativado
- Confirme as permissões do usuário
- Limpe o cache do navegador

#### Erro ao criar produto
- Verifique se as tabelas foram criadas
- Confirme as permissões do banco de dados
- Verifique os logs de erro do WordPress

#### Pagamento não processa
- Confirme as credenciais do gateway
- Verifique se o SSL está ativo
- Teste com valores pequenos primeiro

#### Facebook Pixel não funciona
- Verifique se o ID está correto
- Confirme se o Access Token é válido
- Teste com o Facebook Pixel Helper

### Logs de Erro
Os erros são registrados em:
- **WordPress**: wp-content/debug.log
- **Plugin**: Painel administrativo > TunaPix > Logs

## 📞 Suporte

### Documentação
- [Guia Completo](https://tunapix.com/docs)
- [Vídeos Tutoriais](https://tunapix.com/videos)
- [FAQ](https://tunapix.com/faq)

### Contato
- **E-mail**: <EMAIL>
- **WhatsApp**: +55 11 99999-9999
- **Telefone**: +55 11 3333-3333

### Comunidade
- **Grupo Facebook**: [TunaPix Community](https://facebook.com/groups/tunapix)
- **Telegram**: [@tunapix](https://t.me/tunapix)

## 🔄 Atualizações

### Versão 1.0.0
- ✅ Lançamento inicial
- ✅ Suporte a 6 gateways de pagamento
- ✅ Dashboard completo
- ✅ Temas claro e escuro
- ✅ Facebook Pixel
- ✅ Webhooks
- ✅ Order Bump

### Próximas Versões
- 🔄 Integração com WhatsApp Business
- 🔄 Suporte a cupons de desconto
- 🔄 Checkout em múltiplas etapas
- 🔄 Integração com Google Analytics 4
- 🔄 Suporte a assinaturas recorrentes

## 📄 Licença

Este plugin é licenciado sob a GPL v2 ou posterior.

## 👨‍💻 Desenvolvimento

### Estrutura do Plugin
```
tunapix-checkout/
├── tunapix-checkout.php          # Arquivo principal
├── includes/                      # Classes principais
│   ├── class-tunapix-admin.php
│   ├── class-tunapix-products.php
│   ├── class-tunapix-payments.php
│   ├── class-tunapix-analytics.php
│   ├── class-tunapix-facebook-pixel.php
│   ├── class-tunapix-webhooks.php
│   ├── class-tunapix-settings.php
│   └── gateways/                  # Gateways de pagamento
├── templates/                     # Templates
│   ├── admin/                     # Painel administrativo
│   └── checkout/                  # Página de checkout
├── assets/                        # Recursos estáticos
│   ├── css/
│   └── js/
└── languages/                     # Traduções
```

### Contribuição
1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 🎉 Agradecimentos

Agradecemos a todos os desenvolvedores e usuários que contribuíram para o desenvolvimento deste plugin.

---

**TunaPix Checkout** - Transformando vendas online com tecnologia de ponta! 🚀 