<?php
/**
 * Gateway de Pagamento - CashTime
 * 
 * @package TunaPix_Checkout
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Cashtime {
    
    private $api_key;
    private $api_url = 'https://api.cashtime.com/v1';
    
    public function __construct() {
        $this->api_key = get_option('tunapix_cashtime_api_key', '');
    }
    
    /**
     * Processa pagamento via CashTime
     */
    public function process_payment($order_id, $product, $customer_data) {
        // Implementação futura do CashTime
        return array(
            'success' => false,
            'message' => 'Gateway CashTime ainda não implementado'
        );
    }
    
    /**
     * Processa webhook do CashTime
     */
    public function process_webhook($data) {
        // Implementação futura do webhook CashTime
        return array(
            'success' => false,
            'message' => 'Webhook CashTime ainda não implementado'
        );
    }
    
    /**
     * Retorna métodos de pagamento disponíveis
     */
    public function get_payment_methods() {
        return array(
            'card' => 'Cartão de Crédito/Débito',
            'pix' => 'PIX',
            'boleto' => 'Boleto Bancário'
        );
    }
    
    /**
     * Verifica se o gateway está disponível
     */
    public function is_available() {
        return !empty($this->api_key);
    }
    
    /**
     * Mapeia status do CashTime para status do plugin
     */
    private function map_payment_status($cashtime_status) {
        $status_map = array(
            'approved' => 'completed',
            'pending' => 'pending',
            'declined' => 'failed',
            'cancelled' => 'cancelled',
            'refunded' => 'refunded'
        );
        
        return isset($status_map[$cashtime_status]) ? $status_map[$cashtime_status] : 'pending';
    }
} 