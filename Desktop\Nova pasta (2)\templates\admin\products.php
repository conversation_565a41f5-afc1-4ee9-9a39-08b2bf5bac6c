<?php
/**
 * Template da página de produtos
 * 
 * @package TunaPix_Checkout
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap tunapix-admin">
    <div class="tunapix-header">
        <h1>Gerenciar Produtos</h1>
        <p>Crie e gerencie seus produtos para checkout</p>
    </div>

    <div class="tunapix-content">
        <!-- Botão para criar novo produto -->
        <div class="tunapix-card">
            <div class="card-header">
                <h2>Criar Novo Produto</h2>
            </div>
            <div class="card-body">
                <button type="button" class="button button-primary" id="create-product-btn">
                    <span class="dashicons dashicons-plus-alt"></span>
                    Criar Produto
                </button>
            </div>
        </div>

        <!-- Lista de produtos -->
        <div class="tunapix-card">
            <div class="card-header">
                <h2>Produtos Existentes</h2>
            </div>
            <div class="card-body">
                <div id="products-list">
                    <div class="loading">Carregando produtos...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para criar/editar produto -->
<div id="product-modal" class="tunapix-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modal-title">Criar Produto</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <form id="product-form">
                <input type="hidden" id="product-id" name="id" value="">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="product-name">Nome do Produto *</label>
                        <input type="text" id="product-name" name="name" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="product-description">Descrição</label>
                        <textarea id="product-description" name="description" rows="3"></textarea>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="product-price">Preço *</label>
                        <input type="number" id="product-price" name="price" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="product-currency">Moeda</label>
                        <select id="product-currency" name="currency">
                            <option value="BRL">BRL (R$)</option>
                            <option value="USD">USD ($)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="GBP">GBP (£)</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="product-theme">Tema</label>
                        <select id="product-theme" name="theme">
                            <option value="white">Tema Claro</option>
                            <option value="dark">Tema Escuro</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="product-redirect">URL de Redirecionamento</label>
                        <input type="url" id="product-redirect" name="redirect_url" placeholder="https://...">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="product-banner">Banner do Checkout</label>
                        <input type="file" id="product-banner" name="checkout_banner" accept="image/*">
                        <div id="banner-preview"></div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="product-social-proof">Provas Sociais</label>
                        <textarea id="product-social-proof" name="social_proof" rows="4" placeholder="Depoimentos de clientes..."></textarea>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="product-delivery-email">E-mail de Entrega</label>
                        <textarea id="product-delivery-email" name="delivery_email" rows="6" placeholder="Template do e-mail de entrega..."></textarea>
                        <small>Variáveis disponíveis: {nome}, {produto}, {pedido}, {preco}</small>
                    </div>
                </div>

                <!-- Order Bump -->
                <div class="form-section">
                    <h4>Order Bump (Oferta Complementar)</h4>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="order-bump-enabled" name="order_bump_enabled">
                                Habilitar Order Bump
                            </label>
                        </div>
                    </div>

                    <div id="order-bump-fields" style="display: none;">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="order-bump-name">Nome do Order Bump</label>
                                <input type="text" id="order-bump-name" name="order_bump_name">
                            </div>
                            <div class="form-group">
                                <label for="order-bump-price">Preço do Order Bump</label>
                                <input type="number" id="order-bump-price" name="order_bump_price" step="0.01" min="0">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="order-bump-description">Descrição do Order Bump</label>
                                <textarea id="order-bump-description" name="order_bump_description" rows="3"></textarea>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="order-bump-delivery-email">E-mail de Entrega do Order Bump</label>
                                <textarea id="order-bump-delivery-email" name="order_bump_delivery_email" rows="4"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Facebook Pixels -->
                <div class="form-section">
                    <h4>Facebook Pixels</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-facebook-pixels">IDs dos Pixels (um por linha)</label>
                            <textarea id="product-facebook-pixels" name="facebook_pixels" rows="3" placeholder="123456789012345&#10;987654321098765"></textarea>
                            <small>Deixe em branco para usar os pixels globais</small>
                        </div>
                    </div>
                </div>

                <!-- Campos do Formulário -->
                <div class="form-section">
                    <h4>Campos do Formulário</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="fields_enabled[]" value="name" checked>
                                Nome Completo
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="fields_enabled[]" value="cpf" checked>
                                CPF
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="fields_enabled[]" value="phone" checked>
                                Telefone
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="button button-primary">Salvar Produto</button>
                    <button type="button" class="button button-secondary" id="cancel-product">Cancelar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Carregar produtos
    loadProducts();

    // Abrir modal para criar produto
    $('#create-product-btn').click(function() {
        $('#modal-title').text('Criar Produto');
        $('#product-form')[0].reset();
        $('#product-id').val('');
        $('#product-modal').show();
    });

    // Fechar modal
    $('.close, #cancel-product').click(function() {
        $('#product-modal').hide();
    });

    // Toggle Order Bump
    $('#order-bump-enabled').change(function() {
        if ($(this).is(':checked')) {
            $('#order-bump-fields').show();
        } else {
            $('#order-bump-fields').hide();
        }
    });

    // Preview de imagem
    $('#product-banner').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#banner-preview').html('<img src="' + e.target.result + '" style="max-width: 200px; margin-top: 10px;">');
            };
            reader.readAsDataURL(file);
        }
    });

    // Salvar produto
    $('#product-form').submit(function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        formData.append('action', 'tunapix_create_product');
        formData.append('nonce', tunapix_ajax.nonce);

        $.ajax({
            url: tunapix_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification('Produto salvo com sucesso!', 'success');
                    $('#product-modal').hide();
                    loadProducts();
                } else {
                    showNotification('Erro ao salvar produto: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Erro ao salvar produto', 'error');
            }
        });
    });
});

function loadProducts() {
    $.ajax({
        url: tunapix_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'tunapix_get_products',
            nonce: tunapix_ajax.nonce
        },
        success: function(response) {
            if (response.success) {
                displayProducts(response.data);
            } else {
                $('#products-list').html('<p>Erro ao carregar produtos</p>');
            }
        },
        error: function() {
            $('#products-list').html('<p>Erro ao carregar produtos</p>');
        }
    });
}

function displayProducts(products) {
    if (products.length === 0) {
        $('#products-list').html('<p>Nenhum produto encontrado. Crie seu primeiro produto!</p>');
        return;
    }

    let html = '<table class="wp-list-table widefat fixed striped">';
    html += '<thead><tr>';
    html += '<th>Nome</th>';
    html += '<th>Preço</th>';
    html += '<th>Status</th>';
    html += '<th>Link</th>';
    html += '<th>Ações</th>';
    html += '</tr></thead><tbody>';

    products.forEach(function(product) {
        html += '<tr>';
        html += '<td>' + product.name + '</td>';
        html += '<td>' + product.currency + ' ' + product.price + '</td>';
        html += '<td><span class="status-badge active">Ativo</span></td>';
        html += '<td><button class="button button-small copy-link" data-link="' + product.checkout_url + '">Copiar Link</button></td>';
        html += '<td>';
        html += '<button class="button button-small edit-product" data-id="' + product.id + '">Editar</button> ';
        html += '<button class="button button-small button-link-delete delete-product" data-id="' + product.id + '">Excluir</button>';
        html += '</td>';
        html += '</tr>';
    });

    html += '</tbody></table>';
    $('#products-list').html(html);
}

function showNotification(message, type) {
    const notification = $('<div class="tunapix-notification ' + type + '">' + message + '</div>');
    $('body').append(notification);
    setTimeout(function() {
        notification.fadeOut(function() {
            $(this).remove();
        });
    }, 3000);
}
</script> 