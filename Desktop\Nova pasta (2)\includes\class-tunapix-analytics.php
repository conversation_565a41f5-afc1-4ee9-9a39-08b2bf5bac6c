<?php
/**
 * Classe de Analytics
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Analytics {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Construtor vazio
    }
    
    public function get_analytics($period = 7) {
        global $wpdb;
        
        $end_date = current_time('Y-m-d H:i:s');
        $start_date = date('Y-m-d H:i:s', strtotime("-{$period} days"));
        
        // Total de pedidos
        $total_orders = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}tunapix_orders WHERE created_at BETWEEN %s AND %s",
            $start_date, $end_date
        ));
        
        // Pedidos pagos
        $paid_orders = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}tunapix_orders WHERE payment_status = 'completed' AND created_at BETWEEN %s AND %s",
            $start_date, $end_date
        ));
        
        // Faturamento total
        $total_revenue = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM {$wpdb->prefix}tunapix_orders WHERE payment_status = 'completed' AND created_at BETWEEN %s AND %s",
            $start_date, $end_date
        ));
        
        // Ticket médio
        $average_ticket = $paid_orders > 0 ? $total_revenue / $paid_orders : 0;
        
        // Pedidos reembolsados
        $refunded_orders = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}tunapix_orders WHERE payment_status = 'refunded' AND created_at BETWEEN %s AND %s",
            $start_date, $end_date
        ));
        
        // Taxa de conversão
        $conversion_rate = $total_orders > 0 ? ($paid_orders / $total_orders) * 100 : 0;
        
        // Vendas por dia (últimos 7 dias)
        $daily_sales = $wpdb->get_results($wpdb->prepare(
            "SELECT DATE(created_at) as date, COUNT(*) as orders, SUM(amount) as revenue 
             FROM {$wpdb->prefix}tunapix_orders 
             WHERE payment_status = 'completed' AND created_at BETWEEN %s AND %s 
             GROUP BY DATE(created_at) 
             ORDER BY date",
            $start_date, $end_date
        ));
        
        // Produtos mais vendidos
        $top_products = $wpdb->get_results($wpdb->prepare(
            "SELECT p.name, COUNT(o.id) as sales, SUM(o.amount) as revenue 
             FROM {$wpdb->prefix}tunapix_orders o 
             JOIN {$wpdb->prefix}tunapix_products p ON o.product_id = p.id 
             WHERE o.payment_status = 'completed' AND o.created_at BETWEEN %s AND %s 
             GROUP BY o.product_id 
             ORDER BY sales DESC 
             LIMIT 5",
            $start_date, $end_date
        ));
        
        // Métodos de pagamento mais usados
        $payment_methods = $wpdb->get_results($wpdb->prepare(
            "SELECT payment_method, COUNT(*) as count, SUM(amount) as revenue 
             FROM {$wpdb->prefix}tunapix_orders 
             WHERE payment_status = 'completed' AND created_at BETWEEN %s AND %s 
             GROUP BY payment_method 
             ORDER BY count DESC",
            $start_date, $end_date
        ));
        
        return array(
            'period' => $period,
            'total_orders' => intval($total_orders),
            'paid_orders' => intval($paid_orders),
            'total_revenue' => floatval($total_revenue),
            'average_ticket' => round($average_ticket, 2),
            'refunded_orders' => intval($refunded_orders),
            'conversion_rate' => round($conversion_rate, 2),
            'daily_sales' => $daily_sales,
            'top_products' => $top_products,
            'payment_methods' => $payment_methods
        );
    }
    
    public function get_recent_orders($limit = 10) {
        global $wpdb;
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT o.*, p.name as product_name 
             FROM {$wpdb->prefix}tunapix_orders o 
             JOIN {$wpdb->prefix}tunapix_products p ON o.product_id = p.id 
             ORDER BY o.created_at DESC 
             LIMIT %d",
            $limit
        ));
    }
    
    public function get_order_stats() {
        global $wpdb;
        
        $today = current_time('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $this_month = current_time('Y-m');
        
        // Hoje
        $today_orders = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}tunapix_orders WHERE DATE(created_at) = %s",
            $today
        ));
        
        $today_revenue = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM {$wpdb->prefix}tunapix_orders WHERE payment_status = 'completed' AND DATE(created_at) = %s",
            $today
        ));
        
        // Ontem
        $yesterday_orders = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}tunapix_orders WHERE DATE(created_at) = %s",
            $yesterday
        ));
        
        $yesterday_revenue = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM {$wpdb->prefix}tunapix_orders WHERE payment_status = 'completed' AND DATE(created_at) = %s",
            $yesterday
        ));
        
        // Este mês
        $month_orders = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}tunapix_orders WHERE DATE_FORMAT(created_at, '%%Y-%%m') = %s",
            $this_month
        ));
        
        $month_revenue = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM {$wpdb->prefix}tunapix_orders WHERE payment_status = 'completed' AND DATE_FORMAT(created_at, '%%Y-%%m') = %s",
            $this_month
        ));
        
        return array(
            'today' => array(
                'orders' => intval($today_orders),
                'revenue' => floatval($today_revenue)
            ),
            'yesterday' => array(
                'orders' => intval($yesterday_orders),
                'revenue' => floatval($yesterday_revenue)
            ),
            'this_month' => array(
                'orders' => intval($month_orders),
                'revenue' => floatval($month_revenue)
            )
        );
    }
    
    public function get_conversion_funnel() {
        global $wpdb;
        
        $period = 30; // Últimos 30 dias
        $end_date = current_time('Y-m-d H:i:s');
        $start_date = date('Y-m-d H:i:s', strtotime("-{$period} days"));
        
        // Total de visitas (simulado - você pode integrar com Google Analytics)
        $total_visits = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM {$wpdb->prefix}tunapix_orders WHERE created_at BETWEEN %s AND %s",
            $start_date, $end_date
        )) * 10; // Multiplicador para simular visitas
        
        // Iniciaram checkout
        $initiated_checkout = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}tunapix_orders WHERE created_at BETWEEN %s AND %s",
            $start_date, $end_date
        ));
        
        // Compras completadas
        $completed_purchases = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}tunapix_orders WHERE payment_status = 'completed' AND created_at BETWEEN %s AND %s",
            $start_date, $end_date
        ));
        
        return array(
            'total_visits' => intval($total_visits),
            'initiated_checkout' => intval($initiated_checkout),
            'completed_purchases' => intval($completed_purchases),
            'checkout_rate' => $total_visits > 0 ? round(($initiated_checkout / $total_visits) * 100, 2) : 0,
            'conversion_rate' => $initiated_checkout > 0 ? round(($completed_purchases / $initiated_checkout) * 100, 2) : 0
        );
    }
} 