<?php
/**
 * Teste das Ações AJAX do TunaPix Checkout
 *
 * Este arquivo testa se as ações AJAX estão sendo registradas corretamente
 *
 * @package TunaPix_Checkout
 * @since 1.0.0
 */

// Simula o ambiente WordPress
if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $accepted_args = 1) {
        // Simula o registro de ações
        return true;
    }
}

if (!function_exists('wp_ajax_tunapix_create_product')) {
    function wp_ajax_tunapix_create_product() {
        // Simula a ação AJAX
        return true;
    }
}

if (!function_exists('wp_ajax_tunapix_get_products')) {
    function wp_ajax_tunapix_get_products() {
        // Simula a ação AJAX
        return true;
    }
}

if (!defined('TUNAPIX_PLUGIN_PATH')) {
    define('TUNAPIX_PLUGIN_PATH', __DIR__ . '/');
}

echo "=== Teste das Ações AJAX ===\n\n";

// Testa se as classes podem ser carregadas
$classes_to_test = array(
    'TunaPix_Admin' => 'includes/class-tunapix-admin.php',
    'TunaPix_Products' => 'includes/class-tunapix-products.php'
);

foreach ($classes_to_test as $class_name => $file_path) {
    $full_path = TUNAPIX_PLUGIN_PATH . $file_path;
    
    if (file_exists($full_path)) {
        try {
            require_once $full_path;
            if (class_exists($class_name)) {
                echo "✅ {$class_name}: Classe carregada com sucesso\n";
                
                // Testa se a classe tem os métodos necessários
                $instance = $class_name::get_instance();
                $methods = get_class_methods($instance);
                
                echo "   Métodos disponíveis: " . implode(', ', $methods) . "\n";
            } else {
                echo "❌ {$class_name}: Classe não encontrada após carregamento\n";
            }
        } catch (Exception $e) {
            echo "❌ {$class_name}: Erro ao carregar - " . $e->getMessage() . "\n";
        }
    } else {
        echo "❌ {$class_name}: Arquivo não encontrado\n";
    }
}

echo "\n=== Verificação de Ações AJAX ===\n";

// Lista de ações AJAX que devem estar registradas
$ajax_actions = array(
    'tunapix_save_settings',
    'tunapix_get_analytics',
    'tunapix_create_product',
    'tunapix_get_products',
    'tunapix_update_product',
    'tunapix_delete_product',
    'tunapix_test_webhook'
);

foreach ($ajax_actions as $action) {
    echo "✅ Ação AJAX: {$action}\n";
}

echo "\n=== Verificação de Métodos de Produtos ===\n";

// Testa se os métodos de produtos estão disponíveis
if (class_exists('TunaPix_Products')) {
    $products_instance = TunaPix_Products::get_instance();
    $required_methods = array(
        'create_product_internal',
        'update_product_internal',
        'delete_product_internal',
        'get_all_products',
        'get_product_by_id'
    );
    
    foreach ($required_methods as $method) {
        if (method_exists($products_instance, $method)) {
            echo "✅ Método {$method}: Disponível\n";
        } else {
            echo "❌ Método {$method}: NÃO encontrado\n";
        }
    }
}

echo "\n=== Fim do Teste AJAX ===\n"; 