<?php
/**
 * Gateway de Pagamento - PagSeguro
 * 
 * @package TunaPix_Checkout
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Pagseguro {
    
    private $email;
    private $token;
    private $api_url = 'https://ws.pagseguro.uol.com.br';
    
    public function __construct() {
        $this->email = get_option('tunapix_pagseguro_email', '');
        $this->token = get_option('tunapix_pagseguro_token', '');
    }
    
    /**
     * Processa pagamento via PagSeguro
     */
    public function process_payment($order_id, $product, $customer_data) {
        // Implementação futura do PagSeguro
        return array(
            'success' => false,
            'message' => 'Gateway PagSeguro ainda não implementado'
        );
    }
    
    /**
     * Processa webhook do PagSeguro
     */
    public function process_webhook($data) {
        // Implementação futura do webhook PagSeguro
        return array(
            'success' => false,
            'message' => 'Webhook PagSeguro ainda não implementado'
        );
    }
    
    /**
     * Retorna métodos de pagamento disponíveis
     */
    public function get_payment_methods() {
        return array(
            'card' => 'Cartão de Crédito/Débito',
            'pix' => 'PIX',
            'boleto' => 'Boleto Bancário'
        );
    }
    
    /**
     * Verifica se o gateway está disponível
     */
    public function is_available() {
        return !empty($this->email) && !empty($this->token);
    }
    
    /**
     * Mapeia status do PagSeguro para status do plugin
     */
    private function map_payment_status($pagseguro_status) {
        $status_map = array(
            '1' => 'pending',    // Aguardando pagamento
            '2' => 'pending',    // Em análise
            '3' => 'completed',  // Paga
            '4' => 'completed',  // Disponível
            '5' => 'pending',    // Em disputa
            '6' => 'refunded',   // Devolvida
            '7' => 'cancelled'   // Cancelada
        );
        
        return isset($status_map[$pagseguro_status]) ? $status_map[$pagseguro_status] : 'pending';
    }
} 