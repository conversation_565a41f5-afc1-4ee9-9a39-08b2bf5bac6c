<?php
/**
 * Template da página de configurações
 * 
 * @package TunaPix_Checkout
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap tunapix-admin">
    <div class="tunapix-header">
        <h1>Configurações do TunaPix Checkout</h1>
        <p>Configure as opções gerais do plugin</p>
    </div>

    <div class="tunapix-content">
        <form id="settings-form">
            <!-- Configurações Gerais -->
            <div class="tunapix-card">
                <div class="card-header">
                    <h2>Configurações Gerais</h2>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="currency">Moeda Padrão</label>
                            <select id="currency" name="currency">
                                <option value="BRL">BRL (Real Brasileiro)</option>
                                <option value="USD">USD (Dólar Americano)</option>
                                <option value="EUR">EUR (Euro)</option>
                                <option value="GBP">GBP (Libra Esterlina)</option>
                                <option value="CAD">CAD (Dólar Canadense)</option>
                                <option value="AUD">AUD (Dólar Australiano)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="country">País</label>
                            <select id="country" name="country">
                                <option value="BR">Brasil</option>
                                <option value="US">Estados Unidos</option>
                                <option value="CA">Canadá</option>
                                <option value="AU">Austrália</option>
                                <option value="GB">Reino Unido</option>
                                <option value="DE">Alemanha</option>
                                <option value="FR">França</option>
                                <option value="ES">Espanha</option>
                                <option value="IT">Itália</option>
                                <option value="PT">Portugal</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="theme">Tema Padrão</label>
                            <select id="theme" name="theme">
                                <option value="white">Tema Claro</option>
                                <option value="dark">Tema Escuro</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="redirect_card">URL de Redirecionamento (Cartão)</label>
                            <input type="url" id="redirect_card" name="redirect_card" placeholder="https://...">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="redirect_pix">URL de Redirecionamento (PIX)</label>
                            <input type="url" id="redirect_pix" name="redirect_pix" placeholder="https://...">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Facebook Pixel -->
            <div class="tunapix-card">
                <div class="card-header">
                    <h2>Facebook Pixel</h2>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="facebook_pixels">IDs dos Pixels Globais (um por linha)</label>
                            <textarea id="facebook_pixels" name="facebook_pixels" rows="4" placeholder="123456789012345&#10;987654321098765"></textarea>
                            <small>Estes pixels serão usados em todos os produtos, a menos que o produto tenha pixels específicos configurados.</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="facebook_access_token">Token de Acesso do Facebook (Opcional)</label>
                            <input type="text" id="facebook_access_token" name="facebook_access_token" placeholder="EAA...">
                            <small>Para eventos server-side do Facebook Pixel</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Webhooks -->
            <div class="tunapix-card">
                <div class="card-header">
                    <h2>Webhooks</h2>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="webhook_url">URL do Webhook</label>
                            <input type="url" id="webhook_url" name="webhook_url" placeholder="https://...">
                            <small>URL para onde os dados de transação serão enviados</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="webhook_secret">Chave Secreta do Webhook</label>
                            <input type="text" id="webhook_secret" name="webhook_secret" placeholder="chave-secreta-aleatoria">
                            <small>Chave para validar a autenticidade dos webhooks</small>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <button type="button" class="button button-secondary" id="test-webhook">Testar Webhook</button>
                            <small>Envia um teste para verificar se o webhook está funcionando</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campos do Formulário -->
            <div class="tunapix-card">
                <div class="card-header">
                    <h2>Campos do Formulário</h2>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="fields_enabled[]" value="name" checked>
                                Nome Completo
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="fields_enabled[]" value="cpf" checked>
                                CPF
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="fields_enabled[]" value="phone" checked>
                                Telefone
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gateways de Pagamento -->
            <div class="tunapix-card">
                <div class="card-header">
                    <h2>Gateways de Pagamento</h2>
                </div>
                <div class="card-body">
                    <!-- Mercado Pago -->
                    <div class="gateway-section">
                        <h4>Mercado Pago</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="mercadopago_access_token">Access Token</label>
                                <input type="text" id="mercadopago_access_token" name="mercadopago_access_token" placeholder="TEST-...">
                            </div>
                            <div class="form-group">
                                <label for="mercadopago_public_key">Public Key</label>
                                <input type="text" id="mercadopago_public_key" name="mercadopago_public_key" placeholder="TEST-...">
                            </div>
                        </div>
                    </div>

                    <!-- Stripe -->
                    <div class="gateway-section">
                        <h4>Stripe</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="stripe_secret_key">Secret Key</label>
                                <input type="text" id="stripe_secret_key" name="stripe_secret_key" placeholder="sk_test_...">
                            </div>
                            <div class="form-group">
                                <label for="stripe_publishable_key">Publishable Key</label>
                                <input type="text" id="stripe_publishable_key" name="stripe_publishable_key" placeholder="pk_test_...">
                            </div>
                        </div>
                    </div>

                    <!-- Asaas -->
                    <div class="gateway-section">
                        <h4>Asaas</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="asaas_api_key">API Key</label>
                                <input type="text" id="asaas_api_key" name="asaas_api_key" placeholder="$aact_...">
                            </div>
                        </div>
                    </div>

                    <!-- PagSeguro -->
                    <div class="gateway-section">
                        <h4>PagSeguro</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="pagseguro_email">E-mail</label>
                                <input type="email" id="pagseguro_email" name="pagseguro_email" placeholder="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="pagseguro_token">Token</label>
                                <input type="text" id="pagseguro_token" name="pagseguro_token" placeholder="...">
                            </div>
                        </div>
                    </div>

                    <!-- PushPay -->
                    <div class="gateway-section">
                        <h4>PushPay</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="pushpay_application_key">Application Key</label>
                                <input type="text" id="pushpay_application_key" name="pushpay_application_key" placeholder="...">
                            </div>
                            <div class="form-group">
                                <label for="pushpay_secret_key">Secret Key</label>
                                <input type="text" id="pushpay_secret_key" name="pushpay_secret_key" placeholder="...">
                            </div>
                        </div>
                    </div>

                    <!-- CashTime -->
                    <div class="gateway-section">
                        <h4>CashTime</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="cashtime_api_key">API Key</label>
                                <input type="text" id="cashtime_api_key" name="cashtime_api_key" placeholder="...">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Botões de Ação -->
            <div class="form-actions">
                <button type="submit" class="button button-primary">Salvar Configurações</button>
                <button type="button" class="button button-secondary" id="reset-settings">Restaurar Padrões</button>
            </div>
        </form>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Carregar configurações atuais
    loadSettings();

    // Salvar configurações
    $('#settings-form').submit(function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        formData.append('action', 'tunapix_save_settings');
        formData.append('nonce', tunapix_ajax.nonce);

        $.ajax({
            url: tunapix_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification('Configurações salvas com sucesso!', 'success');
                } else {
                    showNotification('Erro ao salvar configurações: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Erro ao salvar configurações', 'error');
            }
        });
    });

    // Testar webhook
    $('#test-webhook').click(function() {
        const webhookUrl = $('#webhook_url').val();
        const webhookSecret = $('#webhook_secret').val();

        if (!webhookUrl) {
            showNotification('Configure a URL do webhook primeiro', 'error');
            return;
        }

        $.ajax({
            url: tunapix_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'tunapix_test_webhook',
                webhook_url: webhookUrl,
                webhook_secret: webhookSecret,
                nonce: tunapix_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Webhook testado com sucesso!', 'success');
                } else {
                    showNotification('Erro ao testar webhook: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Erro ao testar webhook', 'error');
            }
        });
    });

    // Restaurar configurações padrão
    $('#reset-settings').click(function() {
        if (confirm('Tem certeza que deseja restaurar as configurações padrão? Esta ação não pode ser desfeita.')) {
            // Implementar restauração de configurações padrão
            showNotification('Configurações restauradas para os padrões', 'success');
        }
    });
});

function loadSettings() {
    // Carregar configurações salvas
    const settings = {
        currency: '<?php echo get_option("tunapix_currency", "BRL"); ?>',
        country: '<?php echo get_option("tunapix_country", "BR"); ?>',
        theme: '<?php echo get_option("tunapix_theme", "white"); ?>',
        redirect_card: '<?php echo get_option("tunapix_redirect_card", ""); ?>',
        redirect_pix: '<?php echo get_option("tunapix_redirect_pix", ""); ?>',
        facebook_pixels: '<?php echo get_option("tunapix_facebook_pixels", ""); ?>',
        facebook_access_token: '<?php echo get_option("tunapix_facebook_access_token", ""); ?>',
        webhook_url: '<?php echo get_option("tunapix_webhook_url", ""); ?>',
        webhook_secret: '<?php echo get_option("tunapix_webhook_secret", ""); ?>',
        mercadopago_access_token: '<?php echo get_option("tunapix_mercadopago_access_token", ""); ?>',
        mercadopago_public_key: '<?php echo get_option("tunapix_mercadopago_public_key", ""); ?>',
        stripe_secret_key: '<?php echo get_option("tunapix_stripe_secret_key", ""); ?>',
        stripe_publishable_key: '<?php echo get_option("tunapix_stripe_publishable_key", ""); ?>',
        asaas_api_key: '<?php echo get_option("tunapix_asaas_api_key", ""); ?>',
        pagseguro_email: '<?php echo get_option("tunapix_pagseguro_email", ""); ?>',
        pagseguro_token: '<?php echo get_option("tunapix_pagseguro_token", ""); ?>',
        pushpay_application_key: '<?php echo get_option("tunapix_pushpay_application_key", ""); ?>',
        pushpay_secret_key: '<?php echo get_option("tunapix_pushpay_secret_key", ""); ?>',
        cashtime_api_key: '<?php echo get_option("tunapix_cashtime_api_key", ""); ?>'
    };

    // Aplicar configurações aos campos
    Object.keys(settings).forEach(function(key) {
        const field = $('#' + key);
        if (field.length) {
            if (field.is('select')) {
                field.val(settings[key]);
            } else {
                field.val(settings[key]);
            }
        }
    });

    // Aplicar campos habilitados
    const fieldsEnabled = '<?php echo get_option("tunapix_fields_enabled", "name,cpf,phone"); ?>'.split(',');
    fieldsEnabled.forEach(function(field) {
        $(`input[name="fields_enabled[]"][value="${field}"]`).prop('checked', true);
    });
}

function showNotification(message, type) {
    const notification = $('<div class="tunapix-notification ' + type + '">' + message + '</div>');
    $('body').append(notification);
    setTimeout(function() {
        notification.fadeOut(function() {
            $(this).remove();
        });
    }, 3000);
}
</script> 