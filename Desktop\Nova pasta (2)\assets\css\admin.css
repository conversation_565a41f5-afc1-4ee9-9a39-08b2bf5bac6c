/* TunaPix Admin Styles */

.tunapix-admin {
    margin: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Headers */
.tunapix-admin h1 {
    color: #23282d;
    font-size: 28px;
    margin-bottom: 30px;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.tunapix-admin h2 {
    color: #23282d;
    font-size: 24px;
    margin-bottom: 20px;
}

.tunapix-admin h3 {
    color: #23282d;
    font-size: 18px;
    margin-bottom: 15px;
}

/* Cards e Containers */
.tunapix-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 25px;
}

.tunapix-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e1e5e9;
}

.tunapix-card-title {
    font-size: 20px;
    font-weight: 600;
    color: #23282d;
    margin: 0;
}

/* Formulários */
.tunapix-form {
    max-width: 800px;
}

.tunapix-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.tunapix-form-group {
    margin-bottom: 20px;
}

.tunapix-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.tunapix-form-group input,
.tunapix-form-group select,
.tunapix-form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.tunapix-form-group input:focus,
.tunapix-form-group select:focus,
.tunapix-form-group textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
}

.tunapix-form-group textarea {
    min-height: 120px;
    resize: vertical;
}

/* Botões */
.tunapix-btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s;
    text-align: center;
}

.tunapix-btn-primary {
    background: #0073aa;
    color: white;
}

.tunapix-btn-primary:hover {
    background: #005a87;
    color: white;
}

.tunapix-btn-success {
    background: #28a745;
    color: white;
}

.tunapix-btn-success:hover {
    background: #218838;
    color: white;
}

.tunapix-btn-danger {
    background: #dc3545;
    color: white;
}

.tunapix-btn-danger:hover {
    background: #c82333;
    color: white;
}

.tunapix-btn-secondary {
    background: #6c757d;
    color: white;
}

.tunapix-btn-secondary:hover {
    background: #5a6268;
    color: white;
}

/* Tabelas */
.tunapix-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tunapix-table th,
.tunapix-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

.tunapix-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #555;
}

.tunapix-table tr:hover {
    background: #f8f9fa;
}

/* Status Badges */
.tunapix-status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.tunapix-status.completed {
    background: #d4edda;
    color: #155724;
}

.tunapix-status.pending {
    background: #fff3cd;
    color: #856404;
}

.tunapix-status.failed {
    background: #f8d7da;
    color: #721c24;
}

.tunapix-status.cancelled {
    background: #e2e3e5;
    color: #383d41;
}

/* Toggles */
.tunapix-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.tunapix-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.tunapix-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.tunapix-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .tunapix-slider {
    background-color: #0073aa;
}

input:checked + .tunapix-slider:before {
    transform: translateX(26px);
}

/* Grid Layout */
.tunapix-grid {
    display: grid;
    gap: 20px;
}

.tunapix-grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.tunapix-grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Alertas */
.tunapix-alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 4px solid;
}

.tunapix-alert-success {
    background: #d4edda;
    color: #155724;
    border-left-color: #28a745;
}

.tunapix-alert-error {
    background: #f8d7da;
    color: #721c24;
    border-left-color: #dc3545;
}

.tunapix-alert-warning {
    background: #fff3cd;
    color: #856404;
    border-left-color: #ffc107;
}

.tunapix-alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border-left-color: #17a2b8;
}

/* Loading */
.tunapix-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.tunapix-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: tunapix-spin 1s linear infinite;
}

@keyframes tunapix-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
    .tunapix-form-row {
        grid-template-columns: 1fr;
    }
    
    .tunapix-table {
        font-size: 14px;
    }
    
    .tunapix-table th,
    .tunapix-table td {
        padding: 10px;
    }
    
    .tunapix-card {
        padding: 20px;
    }
}

/* Tabs */
.tunapix-tabs {
    border-bottom: 1px solid #e1e5e9;
    margin-bottom: 20px;
}

.tunapix-tab {
    display: inline-block;
    padding: 12px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.tunapix-tab.active {
    color: #0073aa;
    border-bottom-color: #0073aa;
}

.tunapix-tab:hover {
    color: #0073aa;
}

/* Modal */
.tunapix-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.tunapix-modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    position: relative;
}

.tunapix-modal-close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.tunapix-modal-close:hover {
    color: #333;
}

/* Color Picker */
.tunapix-color-picker {
    display: flex;
    gap: 10px;
    align-items: center;
}

.tunapix-color-picker input[type="color"] {
    width: 50px;
    height: 40px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

/* File Upload */
.tunapix-file-upload {
    border: 2px dashed #e1e5e9;
    border-radius: 6px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.3s;
}

.tunapix-file-upload:hover {
    border-color: #0073aa;
}

.tunapix-file-upload input[type="file"] {
    display: none;
}

/* Code Editor */
.tunapix-code-editor {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
}

/* Tooltips */
.tunapix-tooltip {
    position: relative;
    display: inline-block;
}

.tunapix-tooltip .tunapix-tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tunapix-tooltip:hover .tunapix-tooltiptext {
    visibility: visible;
    opacity: 1;
} 