<?php
/**
 * Teste do Plugin TunaPix Checkout
 * 
 * Este arquivo pode ser usado para testar se o plugin está funcionando corretamente
 * 
 * @package TunaPix_Checkout
 * @since 1.0.0
 */

// Previne acesso direto
if (!defined('ABSPATH')) {
    // Se não estiver no WordPress, simula o ambiente
    if (!function_exists('get_option')) {
        function get_option($option, $default = '') {
            return $default;
        }
    }
    if (!defined('TUNAPIX_PLUGIN_PATH')) {
        define('TUNAPIX_PLUGIN_PATH', __DIR__ . '/');
    }
}

// Testa se os arquivos de gateway existem
$gateway_files = array(
    'mercadopago' => 'includes/gateways/class-tunapix-mercadopago.php',
    'stripe' => 'includes/gateways/class-tunapix-stripe.php',
    'asaas' => 'includes/gateways/class-tunapix-asaas.php',
    'pagseguro' => 'includes/gateways/class-tunapix-pagseguro.php',
    'pushpay' => 'includes/gateways/class-tunapix-pushpay.php',
    'cashtime' => 'includes/gateways/class-tunapix-cashtime.php'
);

echo "=== Teste do Plugin TunaPix Checkout ===\n\n";

// Verifica se os arquivos existem
foreach ($gateway_files as $gateway => $file) {
    $file_path = TUNAPIX_PLUGIN_PATH . $file;
    if (file_exists($file_path)) {
        echo "✅ {$gateway}: Arquivo encontrado\n";
    } else {
        echo "❌ {$gateway}: Arquivo NÃO encontrado\n";
    }
}

echo "\n=== Verificação de Classes ===\n";

// Testa carregamento das classes principais
$main_files = array(
    'Admin' => 'includes/class-tunapix-admin.php',
    'Products' => 'includes/class-tunapix-products.php',
    'Payments' => 'includes/class-tunapix-payments.php',
    'Analytics' => 'includes/class-tunapix-analytics.php',
    'Facebook_Pixel' => 'includes/class-tunapix-facebook-pixel.php',
    'Webhooks' => 'includes/class-tunapix-webhooks.php',
    'Settings' => 'includes/class-tunapix-settings.php',
    'Checkout_Page' => 'includes/class-tunapix-checkout-page.php'
);

foreach ($main_files as $class => $file) {
    $file_path = TUNAPIX_PLUGIN_PATH . $file;
    if (file_exists($file_path)) {
        echo "✅ {$class}: Arquivo encontrado\n";
    } else {
        echo "❌ {$class}: Arquivo NÃO encontrado\n";
    }
}

echo "\n=== Verificação de Templates ===\n";

$template_files = array(
    'Admin Dashboard' => 'templates/admin/dashboard.php',
    'Admin Products' => 'templates/admin/products.php',
    'Admin Settings' => 'templates/admin/settings.php',
    'Checkout Page' => 'templates/checkout/checkout.php'
);

foreach ($template_files as $template => $file) {
    $file_path = TUNAPIX_PLUGIN_PATH . $file;
    if (file_exists($file_path)) {
        echo "✅ {$template}: Arquivo encontrado\n";
    } else {
        echo "❌ {$template}: Arquivo NÃO encontrado\n";
    }
}

echo "\n=== Verificação de Assets ===\n";

$asset_files = array(
    'Admin CSS' => 'assets/css/admin.css',
    'Admin JS' => 'assets/js/admin.js'
);

foreach ($asset_files as $asset => $file) {
    $file_path = TUNAPIX_PLUGIN_PATH . $file;
    if (file_exists($file_path)) {
        echo "✅ {$asset}: Arquivo encontrado\n";
    } else {
        echo "❌ {$asset}: Arquivo NÃO encontrado\n";
    }
}

echo "\n=== Teste de Carregamento de Gateway ===\n";

// Testa carregamento de um gateway específico
$test_gateway = TUNAPIX_PLUGIN_PATH . 'includes/gateways/class-tunapix-mercadopago.php';
if (file_exists($test_gateway)) {
    try {
        require_once $test_gateway;
        if (class_exists('TunaPix_Mercadopago')) {
            echo "✅ MercadoPago: Classe carregada com sucesso\n";
        } else {
            echo "❌ MercadoPago: Classe não encontrada após carregamento\n";
        }
    } catch (Exception $e) {
        echo "❌ MercadoPago: Erro ao carregar - " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ MercadoPago: Arquivo não encontrado\n";
}

echo "\n=== Fim do Teste ===\n"; 