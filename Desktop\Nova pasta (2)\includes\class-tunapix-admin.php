<?php
/**
 * Classe de Administração do TunaPix Checkout
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Admin {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_tunapix_save_settings', array($this, 'save_settings'));
        add_action('wp_ajax_tunapix_get_analytics', array($this, 'get_analytics'));
        
        // Ações AJAX para produtos
        add_action('wp_ajax_tunapix_create_product', array($this, 'create_product'));
        add_action('wp_ajax_tunapix_get_products', array($this, 'get_products'));
        add_action('wp_ajax_tunapix_update_product', array($this, 'update_product'));
        add_action('wp_ajax_tunapix_delete_product', array($this, 'delete_product'));
        add_action('wp_ajax_tunapix_test_webhook', array($this, 'test_webhook'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'TunaPix Checkout',
            'TunaPix',
            'manage_options',
            'tunapix-checkout',
            array($this, 'admin_page'),
            'dashicons-cart',
            30
        );
        
        add_submenu_page(
            'tunapix-checkout',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'tunapix-checkout',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'tunapix-checkout',
            'Produtos',
            'Produtos',
            'manage_options',
            'tunapix-products',
            array($this, 'products_page')
        );
        
        add_submenu_page(
            'tunapix-checkout',
            'Configurações',
            'Configurações',
            'manage_options',
            'tunapix-settings',
            array($this, 'settings_page')
        );
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'tunapix') !== false) {
            wp_enqueue_style('tunapix-admin', TUNAPIX_PLUGIN_URL . 'assets/css/admin.css', array(), TUNAPIX_VERSION);
            wp_enqueue_script('tunapix-admin', TUNAPIX_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), TUNAPIX_VERSION, true);
            wp_localize_script('tunapix-admin', 'tunapix_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('tunapix_nonce')
            ));
        }
    }
    
    public function admin_page() {
        include TUNAPIX_PLUGIN_PATH . 'templates/admin/dashboard.php';
    }
    
    public function products_page() {
        include TUNAPIX_PLUGIN_PATH . 'templates/admin/products.php';
    }
    
    public function settings_page() {
        include TUNAPIX_PLUGIN_PATH . 'templates/admin/settings.php';
    }
    
    public function save_settings() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }
        
        $settings = $_POST['settings'];
        
        foreach ($settings as $key => $value) {
            update_option('tunapix_' . $key, $value);
        }
        
        wp_send_json_success('Configurações salvas com sucesso!');
    }
    
    public function get_analytics() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }
        
        $period = isset($_POST['period']) ? $_POST['period'] : '7';
        $analytics = TunaPix_Analytics::get_instance()->get_analytics($period);
        
        wp_send_json_success($analytics);
    }

    /**
     * Criar novo produto
     */
    public function create_product() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }

        $product_data = array(
            'name' => sanitize_text_field($_POST['name']),
            'description' => sanitize_textarea_field($_POST['description']),
            'price' => floatval($_POST['price']),
            'currency' => sanitize_text_field($_POST['currency']),
            'theme' => sanitize_text_field($_POST['theme']),
            'redirect_url' => esc_url_raw($_POST['redirect_url']),
            'social_proof' => wp_kses_post($_POST['social_proof']),
            'delivery_email' => wp_kses_post($_POST['delivery_email']),
            'order_bump_enabled' => isset($_POST['order_bump_enabled']) ? 1 : 0,
            'order_bump_name' => sanitize_text_field($_POST['order_bump_name']),
            'order_bump_price' => floatval($_POST['order_bump_price']),
            'order_bump_description' => wp_kses_post($_POST['order_bump_description']),
            'order_bump_delivery_email' => wp_kses_post($_POST['order_bump_delivery_email']),
            'facebook_pixels' => sanitize_textarea_field($_POST['facebook_pixels']),
            'fields_enabled' => isset($_POST['fields_enabled']) ? implode(',', array_map('sanitize_text_field', $_POST['fields_enabled'])) : 'name,cpf,phone'
        );

        // Upload do banner se fornecido
        if (!empty($_FILES['checkout_banner']['name'])) {
            $upload = wp_handle_upload($_FILES['checkout_banner'], array('test_form' => false));
            if (!isset($upload['error'])) {
                $product_data['checkout_banner'] = $upload['url'];
            }
        }

        $product_id = TunaPix_Products::get_instance()->create_product_internal($product_data);
        
        if ($product_id) {
            wp_send_json_success(array(
                'message' => 'Produto criado com sucesso!',
                'product_id' => $product_id
            ));
        } else {
            wp_send_json_error('Erro ao criar produto');
        }
    }

    /**
     * Obter lista de produtos
     */
    public function get_products() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }

        $products = TunaPix_Products::get_instance()->get_all_products();
        
        wp_send_json_success($products);
    }

    /**
     * Atualizar produto
     */
    public function update_product() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }

        $product_id = intval($_POST['id']);
        $product_data = array(
            'name' => sanitize_text_field($_POST['name']),
            'description' => sanitize_textarea_field($_POST['description']),
            'price' => floatval($_POST['price']),
            'currency' => sanitize_text_field($_POST['currency']),
            'theme' => sanitize_text_field($_POST['theme']),
            'redirect_url' => esc_url_raw($_POST['redirect_url']),
            'social_proof' => wp_kses_post($_POST['social_proof']),
            'delivery_email' => wp_kses_post($_POST['delivery_email']),
            'order_bump_enabled' => isset($_POST['order_bump_enabled']) ? 1 : 0,
            'order_bump_name' => sanitize_text_field($_POST['order_bump_name']),
            'order_bump_price' => floatval($_POST['order_bump_price']),
            'order_bump_description' => wp_kses_post($_POST['order_bump_description']),
            'order_bump_delivery_email' => wp_kses_post($_POST['order_bump_delivery_email']),
            'facebook_pixels' => sanitize_textarea_field($_POST['facebook_pixels']),
            'fields_enabled' => isset($_POST['fields_enabled']) ? implode(',', array_map('sanitize_text_field', $_POST['fields_enabled'])) : 'name,cpf,phone'
        );

        // Upload do banner se fornecido
        if (!empty($_FILES['checkout_banner']['name'])) {
            $upload = wp_handle_upload($_FILES['checkout_banner'], array('test_form' => false));
            if (!isset($upload['error'])) {
                $product_data['checkout_banner'] = $upload['url'];
            }
        }

        $result = TunaPix_Products::get_instance()->update_product_internal($product_id, $product_data);
        
        if ($result) {
            wp_send_json_success('Produto atualizado com sucesso!');
        } else {
            wp_send_json_error('Erro ao atualizar produto');
        }
    }

    /**
     * Excluir produto
     */
    public function delete_product() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }

        $product_id = intval($_POST['id']);
        $result = TunaPix_Products::get_instance()->delete_product_internal($product_id);
        
        if ($result) {
            wp_send_json_success('Produto excluído com sucesso!');
        } else {
            wp_send_json_error('Erro ao excluir produto');
        }
    }

    /**
     * Testar webhook
     */
    public function test_webhook() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }

        $webhook_url = esc_url_raw($_POST['webhook_url']);
        $webhook_secret = sanitize_text_field($_POST['webhook_secret']);

        if (empty($webhook_url)) {
            wp_send_json_error('URL do webhook é obrigatória');
        }

        $test_data = array(
            'event' => 'test',
            'timestamp' => current_time('timestamp'),
            'message' => 'Teste de webhook do TunaPix Checkout'
        );

        $result = TunaPix_Webhooks::get_instance()->send_webhook($webhook_url, $test_data, $webhook_secret);
        
        if ($result) {
            wp_send_json_success('Webhook testado com sucesso!');
        } else {
            wp_send_json_error('Erro ao testar webhook');
        }
    }
} 