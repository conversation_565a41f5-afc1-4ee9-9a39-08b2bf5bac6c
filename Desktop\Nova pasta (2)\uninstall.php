<?php
/**
 * Arquivo de Desinstalação do TunaPix Checkout
 * 
 * Este arquivo é executado quando o plugin é desinstalado
 */

// Se não foi chamado pelo WordPress, sair
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Verificar se o usuário tem permissão
if (!current_user_can('activate_plugins')) {
    return;
}

// Definir constantes se não existirem
if (!defined('TUNAPIX_PLUGIN_PATH')) {
    define('TUNAPIX_PLUGIN_PATH', plugin_dir_path(__FILE__));
}

// Incluir arquivo principal para acessar as classes
require_once TUNAPIX_PLUGIN_PATH . 'tunapix-checkout.php';

// Função para remover dados do plugin
function tunapix_remove_plugin_data() {
    global $wpdb;
    
    // Remover tabelas do banco de dados
    $tables = array(
        $wpdb->prefix . 'tunapix_products',
        $wpdb->prefix . 'tunapix_orders',
        $wpdb->prefix . 'tunapix_webhook_logs'
    );
    
    foreach ($tables as $table) {
        $wpdb->query("DROP TABLE IF EXISTS $table");
    }
    
    // Remover opções do WordPress
    $options = array(
        'tunapix_currency',
        'tunapix_country',
        'tunapix_theme',
        'tunapix_facebook_pixels',
        'tunapix_facebook_access_token',
        'tunapix_webhook_url',
        'tunapix_webhook_secret',
        'tunapix_fields_enabled',
        'tunapix_payment_gateways',
        'tunapix_mercadopago_access_token',
        'tunapix_mercadopago_public_key',
        'tunapix_stripe_secret_key',
        'tunapix_stripe_publishable_key',
        'tunapix_asaas_api_key',
        'tunapix_pagseguro_email',
        'tunapix_pagseguro_token',
        'tunapix_pushpay_merchant_id',
        'tunapix_pushpay_secret_key',
        'tunapix_cashtime_api_key'
    );
    
    foreach ($options as $option) {
        delete_option($option);
    }
    
    // Remover meta dados de posts (se houver)
    $wpdb->query("DELETE FROM {$wpdb->postmeta} WHERE meta_key LIKE 'tunapix_%'");
    
    // Remover meta dados de usuários (se houver)
    $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'tunapix_%'");
    
    // Limpar cache
    wp_cache_flush();
    
    // Remover arquivos de upload (se configurado)
    $upload_dir = wp_upload_dir();
    $tunapix_upload_dir = $upload_dir['basedir'] . '/tunapix';
    
    if (is_dir($tunapix_upload_dir)) {
        tunapix_remove_directory($tunapix_upload_dir);
    }
}

// Função para remover diretório recursivamente
function tunapix_remove_directory($dir) {
    if (!is_dir($dir)) {
        return;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        
        if (is_dir($path)) {
            tunapix_remove_directory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}

// Função para limpar logs
function tunapix_clear_logs() {
    $log_file = WP_CONTENT_DIR . '/debug-tunapix.log';
    
    if (file_exists($log_file)) {
        unlink($log_file);
    }
}

// Função para remover cron jobs
function tunapix_clear_cron_jobs() {
    $cron_jobs = array(
        'tunapix_cleanup_old_orders',
        'tunapix_send_webhook_retry',
        'tunapix_generate_analytics_report'
    );
    
    foreach ($cron_jobs as $job) {
        wp_clear_scheduled_hook($job);
    }
}

// Função para remover rewrite rules
function tunapix_clear_rewrite_rules() {
    // Remover regras de rewrite específicas do plugin
    $rewrite_rules = get_option('rewrite_rules', array());
    
    foreach ($rewrite_rules as $rule => $query) {
        if (strpos($query, 'tunapix') !== false) {
            unset($rewrite_rules[$rule]);
        }
    }
    
    update_option('rewrite_rules', $rewrite_rules);
    flush_rewrite_rules();
}

// Função para enviar notificação de desinstalação (opcional)
function tunapix_send_uninstall_notification() {
    $admin_email = get_option('admin_email');
    $site_name = get_bloginfo('name');
    $site_url = get_site_url();
    
    $subject = 'TunaPix Checkout - Plugin Desinstalado';
    $message = "O plugin TunaPix Checkout foi desinstalado do site:\n\n";
    $message .= "Site: $site_name\n";
    $message .= "URL: $site_url\n";
    $message .= "Data: " . current_time('Y-m-d H:i:s') . "\n\n";
    $message .= "Todos os dados do plugin foram removidos.\n";
    
    wp_mail($admin_email, $subject, $message);
}

// Executar limpeza
try {
    // Remover dados do plugin
    tunapix_remove_plugin_data();
    
    // Limpar logs
    tunapix_clear_logs();
    
    // Remover cron jobs
    tunapix_clear_cron_jobs();
    
    // Limpar rewrite rules
    tunapix_clear_rewrite_rules();
    
    // Enviar notificação (opcional - descomente se quiser)
    // tunapix_send_uninstall_notification();
    
    // Log de sucesso
    error_log('TunaPix Checkout: Plugin desinstalado com sucesso');
    
} catch (Exception $e) {
    // Log de erro
    error_log('TunaPix Checkout: Erro ao desinstalar plugin - ' . $e->getMessage());
}

// Limpar qualquer cache restante
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
}

// Limpar cache de objetos
if (function_exists('wp_cache_flush_group')) {
    wp_cache_flush_group('tunapix');
}

// Remover transients
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_tunapix_%'");
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_tunapix_%'");

// Finalizar
exit; 