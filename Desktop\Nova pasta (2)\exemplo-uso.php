<?php
/**
 * Exemplo de Uso do TunaPix Checkout
 * 
 * Este arquivo demonstra como usar as funcionalidades do plugin
 * em temas ou outros plugins WordPress.
 */

// Verificar se o plugin está ativo
if (!function_exists('tunapix_checkout')) {
    echo 'O plugin TunaPix Checkout não está ativo.';
    return;
}

// Exemplo 1: Criar um produto programaticamente
function exemplo_criar_produto() {
    global $wpdb;
    
    $dados_produto = array(
        'name' => 'Meu Produto Digital',
        'description' => 'Descrição detalhada do produto',
        'price' => 97.00,
        'currency' => 'BRL',
        'checkout_banner' => 'https://exemplo.com/banner.jpg',
        'social_proof' => 'Mais de 1000 vendas realizadas!',
        'delivery_email' => 'Olá {nome}, obrigado pela compra do {produto}!',
        'order_bump_enabled' => 1,
        'order_bump_name' => 'Bônus Exclusivo',
        'order_bump_price' => 47.00,
        'order_bump_description' => 'Aproveite este bônus especial!',
        'theme' => 'white',
        'custom_colors' => json_encode(array(
            'primary' => '#0073aa',
            'secondary' => '#28a745'
        )),
        'facebook_pixels' => "123456789\n987654321",
        'redirect_url' => 'https://meusite.com/obrigado',
        'fields_enabled' => json_encode(array(
            'name' => 1,
            'cpf' => 1,
            'phone' => 0
        ))
    );
    
    $resultado = $wpdb->insert(
        $wpdb->prefix . 'tunapix_products',
        $dados_produto
    );
    
    if ($resultado) {
        $produto_id = $wpdb->insert_id;
        $checkout_url = home_url('/checkout/' . $produto_id);
        
        echo "Produto criado com sucesso! URL do checkout: $checkout_url";
    }
}

// Exemplo 2: Obter estatísticas de vendas
function exemplo_obter_estatisticas() {
    $analytics = TunaPix_Analytics::get_instance();
    $estatisticas = $analytics->get_analytics(30); // Últimos 30 dias
    
    echo "=== Estatísticas dos Últimos 30 Dias ===\n";
    echo "Total de Pedidos: " . $estatisticas['total_orders'] . "\n";
    echo "Pedidos Pagos: " . $estatisticas['paid_orders'] . "\n";
    echo "Faturamento: R$ " . number_format($estatisticas['total_revenue'], 2, ',', '.') . "\n";
    echo "Ticket Médio: R$ " . number_format($estatisticas['average_ticket'], 2, ',', '.') . "\n";
    echo "Taxa de Conversão: " . $estatisticas['conversion_rate'] . "%\n";
}

// Exemplo 3: Processar pagamento manualmente
function exemplo_processar_pagamento($produto_id, $dados_cliente) {
    $produto = TunaPix_Products::get_instance()->get_product_by_id($produto_id);
    
    if (!$produto) {
        return array('success' => false, 'message' => 'Produto não encontrado');
    }
    
    $resultado = TunaPix_Payments::get_instance()->process_payment($produto, $dados_cliente);
    
    return $resultado;
}

// Exemplo 4: Disparar evento do Facebook Pixel
function exemplo_disparar_pixel($produto_id, $evento) {
    $produto = TunaPix_Products::get_instance()->get_product_by_id($produto_id);
    
    if (!$produto) {
        return false;
    }
    
    switch ($evento) {
        case 'view_content':
            TunaPix_Facebook_Pixel::get_instance()->track_view_content($produto);
            break;
        case 'add_to_cart':
            TunaPix_Facebook_Pixel::get_instance()->track_add_to_cart($produto);
            break;
        case 'purchase':
            $dados_cliente = array(
                'name' => 'João Silva',
                'email' => '<EMAIL>'
            );
            TunaPix_Facebook_Pixel::get_instance()->track_purchase($produto, $dados_cliente);
            break;
    }
}

// Exemplo 5: Enviar webhook personalizado
function exemplo_enviar_webhook($pedido_id, $evento) {
    $resultado = TunaPix_Webhooks::get_instance()->send_webhook($pedido_id, $evento);
    
    if ($resultado) {
        echo "Webhook enviado com sucesso para o pedido #$pedido_id";
    } else {
        echo "Erro ao enviar webhook";
    }
}

// Exemplo 6: Criar botão de checkout personalizado
function exemplo_botao_checkout($produto_id, $texto = 'Comprar Agora') {
    $checkout_url = home_url('/checkout/' . $produto_id);
    
    return sprintf(
        '<a href="%s" class="tunapix-checkout-btn">%s</a>',
        esc_url($checkout_url),
        esc_html($texto)
    );
}

// Exemplo 7: Obter lista de produtos
function exemplo_listar_produtos() {
    global $wpdb;
    
    $produtos = $wpdb->get_results(
        "SELECT * FROM {$wpdb->prefix}tunapix_products ORDER BY created_at DESC"
    );
    
    echo "=== Lista de Produtos ===\n";
    foreach ($produtos as $produto) {
        $checkout_url = home_url('/checkout/' . $produto->id);
        echo "ID: {$produto->id}\n";
        echo "Nome: {$produto->name}\n";
        echo "Preço: R$ " . number_format($produto->price, 2, ',', '.') . "\n";
        echo "URL: $checkout_url\n";
        echo "---\n";
    }
}

// Exemplo 8: Verificar status de um pedido
function exemplo_verificar_pedido($pedido_id) {
    global $wpdb;
    
    $pedido = $wpdb->get_row($wpdb->prepare(
        "SELECT o.*, p.name as product_name 
         FROM {$wpdb->prefix}tunapix_orders o 
         JOIN {$wpdb->prefix}tunapix_products p ON o.product_id = p.id 
         WHERE o.id = %d",
        $pedido_id
    ));
    
    if ($pedido) {
        echo "=== Pedido #$pedido_id ===\n";
        echo "Cliente: {$pedido->customer_name}\n";
        echo "E-mail: {$pedido->customer_email}\n";
        echo "Produto: {$pedido->product_name}\n";
        echo "Valor: R$ " . number_format($pedido->amount, 2, ',', '.') . "\n";
        echo "Status: {$pedido->payment_status}\n";
        echo "Data: " . date('d/m/Y H:i', strtotime($pedido->created_at)) . "\n";
    } else {
        echo "Pedido não encontrado";
    }
}

// Exemplo 9: Configurar gateway de pagamento
function exemplo_configurar_gateway() {
    // Configurar Mercado Pago
    update_option('tunapix_mercadopago_access_token', 'SEU_ACCESS_TOKEN');
    update_option('tunapix_mercadopago_public_key', 'SUA_PUBLIC_KEY');
    
    // Ativar gateway
    $gateways = get_option('tunapix_payment_gateways', array());
    $gateways['mercadopago'] = 1;
    update_option('tunapix_payment_gateways', $gateways);
    
    echo "Gateway Mercado Pago configurado com sucesso!";
}

// Exemplo 10: Personalizar tema do checkout
function exemplo_personalizar_tema() {
    $cores_personalizadas = array(
        'primary' => '#ff6b6b',
        'secondary' => '#4ecdc4',
        'text' => '#2c3e50'
    );
    
    // Aplicar cores personalizadas via CSS
    $css = "
    .tunapix-checkout .btn-primary { background-color: {$cores_personalizadas['primary']}; }
    .tunapix-checkout .btn-secondary { background-color: {$cores_personalizadas['secondary']}; }
    .tunapix-checkout { color: {$cores_personalizadas['text']}; }
    ";
    
    wp_add_inline_style('tunapix-checkout', $css);
}

// Exemplo 11: Criar shortcode para exibir produtos
function shortcode_produtos_tunapix($atts) {
    $atts = shortcode_atts(array(
        'limit' => 5,
        'orderby' => 'created_at',
        'order' => 'DESC'
    ), $atts);
    
    global $wpdb;
    
    $produtos = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}tunapix_products 
         ORDER BY {$atts['orderby']} {$atts['order']} 
         LIMIT %d",
        $atts['limit']
    ));
    
    $output = '<div class="tunapix-produtos">';
    
    foreach ($produtos as $produto) {
        $checkout_url = home_url('/checkout/' . $produto->id);
        
        $output .= '<div class="produto-item">';
        $output .= '<h3>' . esc_html($produto->name) . '</h3>';
        $output .= '<p>' . esc_html($produto->description) . '</p>';
        $output .= '<div class="preco">R$ ' . number_format($produto->price, 2, ',', '.') . '</div>';
        $output .= '<a href="' . esc_url($checkout_url) . '" class="btn-comprar">Comprar Agora</a>';
        $output .= '</div>';
    }
    
    $output .= '</div>';
    
    return $output;
}
add_shortcode('tunapix_produtos', 'shortcode_produtos_tunapix');

// Exemplo 12: Hook para ações após compra
function exemplo_apos_compra($pedido_id) {
    // Enviar e-mail personalizado
    $pedido = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}tunapix_orders WHERE id = %d",
        $pedido_id
    ));
    
    if ($pedido) {
        $assunto = 'Obrigado pela sua compra!';
        $mensagem = "Olá {$pedido->customer_name},\n\n";
        $mensagem .= "Obrigado por comprar conosco!\n";
        $mensagem .= "Seu pedido #$pedido_id foi processado com sucesso.\n\n";
        $mensagem .= "Aguarde o e-mail de entrega com os dados de acesso.\n\n";
        $mensagem .= "Atenciosamente,\nEquipe TunaPix";
        
        wp_mail($pedido->customer_email, $assunto, $mensagem);
    }
}
add_action('tunapix_payment_completed', 'exemplo_apos_compra');

// Exemplo 13: Validar dados antes do pagamento
function exemplo_validar_dados($dados_cliente) {
    $erros = array();
    
    // Validar CPF
    if (!empty($dados_cliente['cpf']) && !validar_cpf($dados_cliente['cpf'])) {
        $erros[] = 'CPF inválido';
    }
    
    // Validar telefone
    if (!empty($dados_cliente['phone']) && !validar_telefone($dados_cliente['phone'])) {
        $erros[] = 'Telefone inválido';
    }
    
    return $erros;
}

function validar_cpf($cpf) {
    // Remove caracteres não numéricos
    $cpf = preg_replace('/[^0-9]/', '', $cpf);
    
    // Verifica se tem 11 dígitos
    if (strlen($cpf) != 11) {
        return false;
    }
    
    // Verifica se todos os dígitos são iguais
    if (preg_match('/^(\d)\1+$/', $cpf)) {
        return false;
    }
    
    // Calcula os dígitos verificadores
    for ($t = 9; $t < 11; $t++) {
        for ($d = 0, $c = 0; $c < $t; $c++) {
            $d += $cpf[$c] * (($t + 1) - $c);
        }
        $d = ((10 * $d) % 11) % 10;
        if ($cpf[$c] != $d) {
            return false;
        }
    }
    
    return true;
}

function validar_telefone($telefone) {
    // Remove caracteres não numéricos
    $telefone = preg_replace('/[^0-9]/', '', $telefone);
    
    // Verifica se tem entre 10 e 11 dígitos
    return strlen($telefone) >= 10 && strlen($telefone) <= 11;
}

// Exemplo 14: Criar relatório personalizado
function exemplo_relatorio_personalizado($data_inicio, $data_fim) {
    global $wpdb;
    
    $relatorio = $wpdb->get_results($wpdb->prepare(
        "SELECT 
            DATE(created_at) as data,
            COUNT(*) as total_pedidos,
            SUM(CASE WHEN payment_status = 'completed' THEN 1 ELSE 0 END) as pedidos_pagos,
            SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as faturamento
         FROM {$wpdb->prefix}tunapix_orders 
         WHERE created_at BETWEEN %s AND %s 
         GROUP BY DATE(created_at) 
         ORDER BY data",
        $data_inicio,
        $data_fim
    ));
    
    echo "=== Relatório de Vendas ===\n";
    echo "Período: " . date('d/m/Y', strtotime($data_inicio)) . " a " . date('d/m/Y', strtotime($data_fim)) . "\n\n";
    
    $total_pedidos = 0;
    $total_pagos = 0;
    $total_faturamento = 0;
    
    foreach ($relatorio as $linha) {
        echo "Data: " . date('d/m/Y', strtotime($linha->data)) . "\n";
        echo "  Pedidos: {$linha->total_pedidos}\n";
        echo "  Pagos: {$linha->pedidos_pagos}\n";
        echo "  Faturamento: R$ " . number_format($linha->faturamento, 2, ',', '.') . "\n";
        echo "---\n";
        
        $total_pedidos += $linha->total_pedidos;
        $total_pagos += $linha->pedidos_pagos;
        $total_faturamento += $linha->faturamento;
    }
    
    echo "\n=== RESUMO ===\n";
    echo "Total de Pedidos: $total_pedidos\n";
    echo "Total de Pagos: $total_pagos\n";
    echo "Faturamento Total: R$ " . number_format($total_faturamento, 2, ',', '.') . "\n";
    echo "Taxa de Conversão: " . ($total_pedidos > 0 ? round(($total_pagos / $total_pedidos) * 100, 2) : 0) . "%\n";
}

// Exemplo de uso das funções
if (isset($_GET['exemplo'])) {
    switch ($_GET['exemplo']) {
        case 'criar_produto':
            exemplo_criar_produto();
            break;
        case 'estatisticas':
            exemplo_obter_estatisticas();
            break;
        case 'listar_produtos':
            exemplo_listar_produtos();
            break;
        case 'relatorio':
            $data_inicio = date('Y-m-d', strtotime('-30 days'));
            $data_fim = date('Y-m-d');
            exemplo_relatorio_personalizado($data_inicio, $data_fim);
            break;
    }
}

// CSS para os exemplos
function exemplo_css() {
    echo '<style>
    .tunapix-checkout-btn {
        display: inline-block;
        padding: 12px 24px;
        background: #0073aa;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-weight: 500;
        transition: background 0.3s;
    }
    
    .tunapix-checkout-btn:hover {
        background: #005a87;
        color: white;
    }
    
    .tunapix-produtos {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    
    .produto-item {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .produto-item h3 {
        margin: 0 0 10px 0;
        color: #333;
    }
    
    .produto-item .preco {
        font-size: 24px;
        font-weight: bold;
        color: #28a745;
        margin: 15px 0;
    }
    
    .produto-item .btn-comprar {
        display: inline-block;
        padding: 10px 20px;
        background: #0073aa;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        transition: background 0.3s;
    }
    
    .produto-item .btn-comprar:hover {
        background: #005a87;
        color: white;
    }
    </style>';
}
add_action('wp_head', 'exemplo_css'); 