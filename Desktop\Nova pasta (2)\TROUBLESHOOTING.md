# 🔧 Solução de Problemas - TunaPix Checkout

Este guia ajuda a resolver problemas comuns do plugin TunaPix Checkout.

## 🚨 Erro: "Failed to open stream: No such file or directory"

### Problema
```
Warning: require_once(/path/to/class-tunapix-stripe.php): Failed to open stream: No such file or directory
```

### Solução
1. **Verifique se todos os arquivos de gateway foram criados:**
   - `includes/gateways/class-tunapix-mercadopago.php` ✅
   - `includes/gateways/class-tunapix-stripe.php` ✅
   - `includes/gateways/class-tunapix-asaas.php` ✅
   - `includes/gateways/class-tunapix-pagseguro.php` ✅
   - `includes/gateways/class-tunapix-pushpay.php` ✅
   - `includes/gateways/class-tunapix-cashtime.php` ✅

2. **Execute o arquivo de teste:**
   ```bash
   php test-plugin.php
   ```

3. **Verifique permissões de arquivo:**
   ```bash
   chmod 644 includes/gateways/*.php
   ```

## 🔧 Problemas Comuns

### 1. Plugin não aparece no painel admin

**Causa:** Erro fatal durante a inicialização
**Solução:**
- Verifique os logs de erro do WordPress
- Desative outros plugins temporariamente
- Verifique se o PHP tem permissão para ler os arquivos

### 2. Página de checkout não carrega

**Causa:** Problema com rewrite rules
**Solução:**
- Vá em Configurações > Links Permanentes
- Clique em "Salvar Alterações" (mesmo sem mudar nada)

### 3. Gateway de pagamento não funciona

**Causa:** Credenciais incorretas ou gateway não configurado
**Solução:**
- Verifique as credenciais no painel admin
- Teste com o Mercado Pago primeiro (mais estável)
- Verifique se o gateway está habilitado

### 4. Facebook Pixel não dispara

**Causa:** Pixel ID incorreto ou configuração inválida
**Solução:**
- Verifique se o Pixel ID está correto
- Use o Facebook Pixel Helper para debug
- Verifique se não há bloqueadores de anúncios

### 5. Webhooks não funcionam

**Causa:** URL ou chave secreta incorreta
**Solução:**
- Teste a URL do webhook
- Verifique se a chave secreta está correta
- Use ferramentas como webhook.site para testar

## 🛠️ Debug Avançado

### Habilitar Debug do WordPress

Adicione ao `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Verificar Logs

Os logs ficam em: `wp-content/debug.log`

### Teste de Conectividade

```php
// Teste de conexão com gateway
$gateway = new TunaPix_Mercadopago();
if ($gateway->is_available()) {
    echo "Gateway disponível";
} else {
    echo "Gateway não configurado";
}
```

## 📞 Suporte

Se os problemas persistirem:

1. **Execute o teste:** `php test-plugin.php`
2. **Verifique os logs:** `wp-content/debug.log`
3. **Documente o erro:** Screenshot + mensagem exata
4. **Entre em contato:** [Seu email de suporte]

## 🔄 Reinstalação Limpa

Se nada funcionar:

1. **Desative o plugin**
2. **Delete todos os arquivos**
3. **Limpe o banco de dados:**
   ```sql
   DROP TABLE IF EXISTS wp_tunapix_products;
   DROP TABLE IF EXISTS wp_tunapix_orders;
   DELETE FROM wp_options WHERE option_name LIKE 'tunapix_%';
   ```
4. **Reinstale o plugin**

## ✅ Checklist de Verificação

- [ ] Todos os arquivos de gateway existem
- [ ] Permissões de arquivo corretas (644)
- [ ] Credenciais de gateway configuradas
- [ ] Rewrite rules atualizadas
- [ ] Debug habilitado (se necessário)
- [ ] Logs verificados
- [ ] Teste executado com sucesso

---

**Última atualização:** Janeiro 2024
**Versão do plugin:** 1.0.0 