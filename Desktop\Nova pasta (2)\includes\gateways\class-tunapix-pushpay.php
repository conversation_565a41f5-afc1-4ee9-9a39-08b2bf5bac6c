<?php
/**
 * Gateway de Pagamento - PushPay
 * 
 * @package TunaPix_Checkout
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Pushpay {
    
    private $application_key;
    private $secret_key;
    private $api_url = 'https://api.pushpay.com/v1';
    
    public function __construct() {
        $this->application_key = get_option('tunapix_pushpay_application_key', '');
        $this->secret_key = get_option('tunapix_pushpay_secret_key', '');
    }
    
    /**
     * Processa pagamento via PushPay
     */
    public function process_payment($order_id, $product, $customer_data) {
        // Implementação futura do PushPay
        return array(
            'success' => false,
            'message' => 'Gateway PushPay ainda não implementado'
        );
    }
    
    /**
     * Processa webhook do PushPay
     */
    public function process_webhook($data) {
        // Implementação futura do webhook PushPay
        return array(
            'success' => false,
            'message' => 'Webhook PushPay ainda não implementado'
        );
    }
    
    /**
     * Retorna métodos de pagamento disponíveis
     */
    public function get_payment_methods() {
        return array(
            'card' => 'Cartão de Crédito/Débito',
            'ach' => 'Transferência Bancária (ACH)'
        );
    }
    
    /**
     * Verifica se o gateway está disponível
     */
    public function is_available() {
        return !empty($this->application_key) && !empty($this->secret_key);
    }
    
    /**
     * Mapeia status do PushPay para status do plugin
     */
    private function map_payment_status($pushpay_status) {
        $status_map = array(
            'Succeeded' => 'completed',
            'Pending' => 'pending',
            'Failed' => 'failed',
            'Cancelled' => 'cancelled',
            'Refunded' => 'refunded'
        );
        
        return isset($status_map[$pushpay_status]) ? $status_map[$pushpay_status] : 'pending';
    }
} 