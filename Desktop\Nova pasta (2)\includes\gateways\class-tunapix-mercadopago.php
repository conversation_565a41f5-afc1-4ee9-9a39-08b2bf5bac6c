<?php
/**
 * Gateway de Pagamento - Mercado Pago
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Mercadopago {
    
    private $access_token;
    private $public_key;
    private $api_url = 'https://api.mercadopago.com';
    
    public function __construct() {
        $this->access_token = get_option('tunapix_mercadopago_access_token', '');
        $this->public_key = get_option('tunapix_mercadopago_public_key', '');
    }
    
    public function process_payment($order_id, $product, $customer_data) {
        // Criar preferência de pagamento
        $preference_data = array(
            'items' => array(
                array(
                    'title' => $product->name,
                    'quantity' => 1,
                    'unit_price' => floatval($product->price),
                    'currency_id' => $this->get_currency_id($product->currency)
                )
            ),
            'payer' => array(
                'name' => $customer_data['name'],
                'email' => $customer_data['email']
            ),
            'external_reference' => $order_id,
            'notification_url' => home_url('/wp-json/tunapix/v1/webhook/mercadopago'),
            'back_urls' => array(
                'success' => $product->redirect_url ?: home_url('/obrigado/'),
                'failure' => home_url('/checkout/' . $product->id . '?error=payment_failed'),
                'pending' => home_url('/checkout/' . $product->id . '?status=pending')
            ),
            'auto_return' => 'approved',
            'expires' => true,
            'expiration_date_to' => date('c', strtotime('+24 hours'))
        );
        
        // Adicionar order bump se selecionado
        if ($customer_data['order_bump_purchased'] && $product->order_bump_enabled) {
            $preference_data['items'][] = array(
                'title' => $product->order_bump_name,
                'quantity' => 1,
                'unit_price' => floatval($product->order_bump_price),
                'currency_id' => $this->get_currency_id($product->currency)
            );
        }
        
        $response = wp_remote_post($this->api_url . '/checkout/preferences', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->access_token,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($preference_data),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => 'Erro ao conectar com Mercado Pago: ' . $response->get_error_message()
            );
        }
        
        $body = json_decode(wp_remote_retrieve_body($response), true);
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 201 && isset($body['id'])) {
            return array(
                'success' => true,
                'transaction_id' => $body['id'],
                'redirect_url' => $body['init_point']
            );
        } else {
            $error_message = isset($body['message']) ? $body['message'] : 'Erro desconhecido';
            return array(
                'success' => false,
                'message' => 'Erro do Mercado Pago: ' . $error_message
            );
        }
    }
    
    public function process_webhook($data) {
        if (!isset($data['type']) || $data['type'] !== 'payment') {
            return false;
        }
        
        $payment_id = $data['data']['id'];
        
        // Buscar informações do pagamento
        $response = wp_remote_get($this->api_url . '/v1/payments/' . $payment_id, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->access_token
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $payment_data = json_decode(wp_remote_retrieve_body($response), true);
        
        if (!$payment_data) {
            return false;
        }
        
        global $wpdb;
        
        // Buscar pedido pelo external_reference
        $order = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}tunapix_orders WHERE id = %d",
            $payment_data['external_reference']
        ));
        
        if (!$order) {
            return false;
        }
        
        // Atualizar status do pedido
        $status = $this->map_payment_status($payment_data['status']);
        
        $wpdb->update(
            $wpdb->prefix . 'tunapix_orders',
            array(
                'payment_status' => $status,
                'gateway_transaction_id' => $payment_id,
                'updated_at' => current_time('mysql')
            ),
            array('id' => $order->id),
            array('%s', '%s', '%s'),
            array('%d')
        );
        
        // Enviar webhook se necessário
        if ($status === 'completed') {
            TunaPix_Webhooks::get_instance()->send_webhook($order->id, 'purchase');
        }
        
        return true;
    }
    
    private function get_currency_id($currency) {
        $currencies = array(
            'BRL' => 'BRL',
            'ARS' => 'ARS',
            'CLP' => 'CLP',
            'COP' => 'COP',
            'MXN' => 'MXN',
            'PEN' => 'PEN',
            'UYU' => 'UYU'
        );
        
        return isset($currencies[$currency]) ? $currencies[$currency] : 'BRL';
    }
    
    private function map_payment_status($mp_status) {
        $status_map = array(
            'approved' => 'completed',
            'pending' => 'pending',
            'in_process' => 'pending',
            'rejected' => 'failed',
            'cancelled' => 'cancelled',
            'refunded' => 'refunded'
        );
        
        return isset($status_map[$mp_status]) ? $status_map[$mp_status] : 'pending';
    }
    
    public function get_payment_methods() {
        return array(
            'credit_card' => 'Cartão de Crédito',
            'debit_card' => 'Cartão de Débito',
            'pix' => 'PIX',
            'boleto' => 'Boleto',
            'transfer' => 'Transferência'
        );
    }
    
    public function is_available() {
        return !empty($this->access_token) && !empty($this->public_key);
    }
} 