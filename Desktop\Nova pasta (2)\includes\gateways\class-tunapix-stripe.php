<?php
/**
 * Gateway de Pagamento - Stripe
 * 
 * @package TunaPix_Checkout
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Stripe {
    
    private $secret_key;
    private $publishable_key;
    private $api_url = 'https://api.stripe.com/v1';
    
    public function __construct() {
        $this->secret_key = get_option('tunapix_stripe_secret_key', '');
        $this->publishable_key = get_option('tunapix_stripe_publishable_key', '');
    }
    
    /**
     * Processa pagamento via Stripe
     */
    public function process_payment($order_id, $product, $customer_data) {
        // Implementação futura do Stripe
        return array(
            'success' => false,
            'message' => 'Gateway Stripe ainda não implementado'
        );
    }
    
    /**
     * Processa webhook do Stripe
     */
    public function process_webhook($data) {
        // Implementação futura do webhook Stripe
        return array(
            'success' => false,
            'message' => 'Webhook Stripe ainda não implementado'
        );
    }
    
    /**
     * Retorna métodos de pagamento disponíveis
     */
    public function get_payment_methods() {
        return array(
            'card' => 'Cartão de Crédito/Débito',
            'pix' => 'PIX (Brasil)'
        );
    }
    
    /**
     * Verifica se o gateway está disponível
     */
    public function is_available() {
        return !empty($this->secret_key) && !empty($this->publishable_key);
    }
    
    /**
     * Mapeia status do Stripe para status do plugin
     */
    private function map_payment_status($stripe_status) {
        $status_map = array(
            'succeeded' => 'completed',
            'processing' => 'pending',
            'requires_payment_method' => 'pending',
            'requires_confirmation' => 'pending',
            'requires_action' => 'pending',
            'canceled' => 'cancelled',
            'failed' => 'failed'
        );
        
        return isset($status_map[$stripe_status]) ? $status_map[$stripe_status] : 'pending';
    }
} 