<?php
/**
 * Classe de Gerenciamento de Produtos
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Products {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_ajax_tunapix_create_product', array($this, 'create_product'));
        add_action('wp_ajax_tunapix_update_product', array($this, 'update_product'));
        add_action('wp_ajax_tunapix_delete_product', array($this, 'delete_product'));
        add_action('wp_ajax_tunapix_get_product', array($this, 'get_product'));
        add_action('wp_ajax_tunapix_get_products', array($this, 'get_products'));
    }
    
    public function create_product() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }
        
        global $wpdb;
        
        $data = array(
            'name' => sanitize_text_field($_POST['name']),
            'description' => sanitize_textarea_field($_POST['description']),
            'price' => floatval($_POST['price']),
            'currency' => sanitize_text_field($_POST['currency']),
            'checkout_banner' => esc_url_raw($_POST['checkout_banner']),
            'social_proof' => sanitize_textarea_field($_POST['social_proof']),
            'delivery_email' => wp_kses_post($_POST['delivery_email']),
            'order_bump_enabled' => intval($_POST['order_bump_enabled']),
            'order_bump_name' => sanitize_text_field($_POST['order_bump_name']),
            'order_bump_price' => floatval($_POST['order_bump_price']),
            'order_bump_description' => sanitize_textarea_field($_POST['order_bump_description']),
            'order_bump_delivery_email' => wp_kses_post($_POST['order_bump_delivery_email']),
            'theme' => sanitize_text_field($_POST['theme']),
            'custom_colors' => json_encode($_POST['custom_colors']),
            'facebook_pixels' => sanitize_textarea_field($_POST['facebook_pixels']),
            'redirect_url' => esc_url_raw($_POST['redirect_url']),
            'fields_enabled' => json_encode($_POST['fields_enabled'])
        );
        
        $result = $wpdb->insert(
            $wpdb->prefix . 'tunapix_products',
            $data,
            array('%s', '%s', '%f', '%s', '%s', '%s', '%s', '%d', '%s', '%f', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result) {
            $product_id = $wpdb->insert_id;
            $checkout_url = home_url('/checkout/' . $product_id);
            
            wp_send_json_success(array(
                'message' => 'Produto criado com sucesso!',
                'product_id' => $product_id,
                'checkout_url' => $checkout_url
            ));
        } else {
            wp_send_json_error('Erro ao criar produto');
        }
    }
    
    public function update_product() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }
        
        global $wpdb;
        
        $product_id = intval($_POST['product_id']);
        
        $data = array(
            'name' => sanitize_text_field($_POST['name']),
            'description' => sanitize_textarea_field($_POST['description']),
            'price' => floatval($_POST['price']),
            'currency' => sanitize_text_field($_POST['currency']),
            'checkout_banner' => esc_url_raw($_POST['checkout_banner']),
            'social_proof' => sanitize_textarea_field($_POST['social_proof']),
            'delivery_email' => wp_kses_post($_POST['delivery_email']),
            'order_bump_enabled' => intval($_POST['order_bump_enabled']),
            'order_bump_name' => sanitize_text_field($_POST['order_bump_name']),
            'order_bump_price' => floatval($_POST['order_bump_price']),
            'order_bump_description' => sanitize_textarea_field($_POST['order_bump_description']),
            'order_bump_delivery_email' => wp_kses_post($_POST['order_bump_delivery_email']),
            'theme' => sanitize_text_field($_POST['theme']),
            'custom_colors' => json_encode($_POST['custom_colors']),
            'facebook_pixels' => sanitize_textarea_field($_POST['facebook_pixels']),
            'redirect_url' => esc_url_raw($_POST['redirect_url']),
            'fields_enabled' => json_encode($_POST['fields_enabled'])
        );
        
        $result = $wpdb->update(
            $wpdb->prefix . 'tunapix_products',
            $data,
            array('id' => $product_id),
            array('%s', '%s', '%f', '%s', '%s', '%s', '%s', '%d', '%s', '%f', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s'),
            array('%d')
        );
        
        if ($result !== false) {
            wp_send_json_success('Produto atualizado com sucesso!');
        } else {
            wp_send_json_error('Erro ao atualizar produto');
        }
    }
    
    public function delete_product() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }
        
        global $wpdb;
        
        $product_id = intval($_POST['product_id']);
        
        $result = $wpdb->delete(
            $wpdb->prefix . 'tunapix_products',
            array('id' => $product_id),
            array('%d')
        );
        
        if ($result) {
            wp_send_json_success('Produto excluído com sucesso!');
        } else {
            wp_send_json_error('Erro ao excluir produto');
        }
    }
    
    public function get_product() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }
        
        global $wpdb;
        
        $product_id = intval($_POST['product_id']);
        
        $product = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}tunapix_products WHERE id = %d",
                $product_id
            )
        );
        
        if ($product) {
            $product->custom_colors = json_decode($product->custom_colors, true);
            $product->fields_enabled = json_decode($product->fields_enabled, true);
            wp_send_json_success($product);
        } else {
            wp_send_json_error('Produto não encontrado');
        }
    }
    
    public function get_products() {
        check_ajax_referer('tunapix_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }
        
        global $wpdb;
        
        $products = $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}tunapix_products ORDER BY created_at DESC"
        );
        
        foreach ($products as $product) {
            $product->checkout_url = home_url('/checkout/' . $product->id);
        }
        
        wp_send_json_success($products);
    }
    
    public function get_product_by_id($product_id) {
        global $wpdb;
        
        $product = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}tunapix_products WHERE id = %d",
                $product_id
            )
        );
        
        if ($product) {
            $product->custom_colors = json_decode($product->custom_colors, true);
            $product->fields_enabled = json_decode($product->fields_enabled, true);
        }
        
        return $product;
    }

    /**
     * Criar produto (método público para uso interno)
     */
    public function create_product_internal($data) {
        global $wpdb;
        
        $result = $wpdb->insert(
            $wpdb->prefix . 'tunapix_products',
            $data,
            array('%s', '%s', '%f', '%s', '%s', '%s', '%s', '%d', '%s', '%f', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result) {
            return $wpdb->insert_id;
        }
        
        return false;
    }

    /**
     * Atualizar produto (método público para uso interno)
     */
    public function update_product_internal($product_id, $data) {
        global $wpdb;
        
        $result = $wpdb->update(
            $wpdb->prefix . 'tunapix_products',
            $data,
            array('id' => $product_id),
            array('%s', '%s', '%f', '%s', '%s', '%s', '%s', '%d', '%s', '%f', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s'),
            array('%d')
        );
        
        return $result !== false;
    }

    /**
     * Excluir produto (método público para uso interno)
     */
    public function delete_product_internal($product_id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $wpdb->prefix . 'tunapix_products',
            array('id' => $product_id),
            array('%d')
        );
        
        return $result !== false;
    }

    /**
     * Obter todos os produtos (método público para uso interno)
     */
    public function get_all_products() {
        global $wpdb;
        
        $products = $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}tunapix_products ORDER BY created_at DESC"
        );
        
        foreach ($products as $product) {
            $product->custom_colors = json_decode($product->custom_colors, true);
            $product->fields_enabled = json_decode($product->fields_enabled, true);
        }
        
        return $products;
    }
} 