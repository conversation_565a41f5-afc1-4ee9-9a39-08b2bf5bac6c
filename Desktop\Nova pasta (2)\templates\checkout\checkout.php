<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo esc_html($tunapix_product->name); ?> - Checkout</title>
    
    <!-- Estilos personalizados -->
    <style>
        <?php echo TunaPix_Checkout_Page::get_instance()->get_checkout_styles($tunapix_product); ?>
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        .tunapix-checkout {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* Banner Superior */
        .checkout-banner {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .banner-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .banner-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .banner-right {
            font-size: 18px;
            font-weight: bold;
        }
        
        .countdown {
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 25px;
            font-size: 14px;
        }
        
        /* Container Principal */
        .checkout-container {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            gap: 40px;
        }
        
        /* Coluna Esquerda - Formulário */
        .checkout-form {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .product-info {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .product-image {
            width: 100%;
            max-width: 300px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .product-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .product-description {
            color: #666;
            margin-bottom: 15px;
        }
        
        .product-price {
            font-size: 32px;
            font-weight: bold;
            color: #28a745;
        }
        
        /* Formulário */
        .form-section {
            margin-bottom: 25px;
        }
        
        .form-section h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 18px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #0073aa;
        }
        
        /* Métodos de Pagamento */
        .payment-methods {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .payment-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .payment-tab.active {
            border-color: #0073aa;
            background: #0073aa;
            color: white;
        }
        
        /* Order Bump */
        .order-bump {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .order-bump-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .order-bump-checkbox {
            width: 20px;
            height: 20px;
        }
        
        .order-bump-title {
            font-weight: bold;
            font-size: 18px;
        }
        
        .order-bump-price {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }
        
        .order-bump-description {
            color: #666;
            line-height: 1.5;
        }
        
        /* Botão de Finalização */
        .checkout-button {
            width: 100%;
            padding: 18px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .checkout-button:hover {
            background: #218838;
        }
        
        .checkout-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        /* Coluna Direita - Benefícios */
        .benefits-column {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .benefits-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .benefits-list {
            list-style: none;
            margin-bottom: 30px;
        }
        
        .benefits-list li {
            padding: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .benefits-list li:before {
            content: "✓";
            background: rgba(255,255,255,0.2);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .guarantee-box {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .guarantee-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .guarantee-text {
            font-size: 14px;
            opacity: 0.9;
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .checkout-container {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .banner-content {
                flex-direction: column;
                gap: 10px;
            }
            
            .checkout-form {
                padding: 20px;
            }
            
            .benefits-column {
                padding: 30px 20px;
            }
        }
        
        /* Loading */
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #0073aa;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Mensagens de erro/sucesso */
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="tunapix-checkout">
        <!-- Banner Superior -->
        <div class="checkout-banner">
            <div class="banner-content">
                <div class="banner-left">
                    <span>⏰</span>
                    <span>OFERTA POR TEMPO LIMITADO</span>
                </div>
                <div class="banner-right">
                    <div class="countdown" id="countdown">
                        <span id="hours">00</span>h : <span id="minutes">00</span>m : <span id="seconds">00</span>s
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Container Principal -->
        <div class="checkout-container">
            <!-- Coluna Esquerda - Formulário -->
            <div class="checkout-form">
                <!-- Informações do Produto -->
                <div class="product-info">
                    <?php if ($tunapix_product->checkout_banner): ?>
                        <img src="<?php echo esc_url($tunapix_product->checkout_banner); ?>" alt="<?php echo esc_attr($tunapix_product->name); ?>" class="product-image">
                    <?php endif; ?>
                    
                    <h1 class="product-title"><?php echo esc_html($tunapix_product->name); ?></h1>
                    <p class="product-description"><?php echo esc_html($tunapix_product->description); ?></p>
                    <div class="product-price">
                        <?php 
                        $currency_symbol = TunaPix_Checkout_Page::get_instance()->get_currency_symbol();
                        echo $currency_symbol . ' ' . number_format($tunapix_product->price, 2, ',', '.');
                        ?>
                    </div>
                </div>
                
                <!-- Mensagens -->
                <div id="message" class="message"></div>
                
                <!-- Formulário de Checkout -->
                <form id="checkout-form">
                    <input type="hidden" name="product_id" value="<?php echo $tunapix_product->id; ?>">
                    
                    <!-- Dados Pessoais -->
                    <div class="form-section">
                        <h3>Dados Pessoais</h3>
                        
                        <div class="form-group">
                            <label for="customer_name">Nome Completo *</label>
                            <input type="text" id="customer_name" name="customer_name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="customer_email">E-mail *</label>
                            <input type="email" id="customer_email" name="customer_email" required>
                        </div>
                        
                        <?php if ($tunapix_product->fields_enabled['cpf']): ?>
                        <div class="form-group">
                            <label for="customer_cpf">CPF</label>
                            <input type="text" id="customer_cpf" name="customer_cpf" placeholder="000.000.000-00">
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($tunapix_product->fields_enabled['phone']): ?>
                        <div class="form-group">
                            <label for="customer_phone">Telefone</label>
                            <input type="tel" id="customer_phone" name="customer_phone" placeholder="(00) 00000-0000">
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Métodos de Pagamento -->
                    <div class="form-section">
                        <h3>Forma de Pagamento</h3>
                        
                        <div class="payment-methods">
                            <div class="payment-tab active" data-method="card">
                                <span>💳</span><br>
                                Cartão
                            </div>
                            <div class="payment-tab" data-method="pix">
                                <span>📱</span><br>
                                PIX
                            </div>
                        </div>
                        
                        <input type="hidden" name="payment_method" value="card" id="payment_method">
                    </div>
                    
                    <!-- Order Bump -->
                    <?php if ($tunapix_product->order_bump_enabled): ?>
                    <div class="order-bump">
                        <div class="order-bump-header">
                            <input type="checkbox" id="order_bump" name="order_bump_purchased" class="order-bump-checkbox" value="1">
                            <div>
                                <div class="order-bump-title"><?php echo esc_html($tunapix_product->order_bump_name); ?></div>
                                <div class="order-bump-price">
                                    <?php echo $currency_symbol . ' ' . number_format($tunapix_product->order_bump_price, 2, ',', '.'); ?>
                                </div>
                            </div>
                        </div>
                        <div class="order-bump-description">
                            <?php echo esc_html($tunapix_product->order_bump_description); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Botão de Finalização -->
                    <button type="submit" class="checkout-button" id="checkout-button">
                        Finalizar Compra - <?php echo $currency_symbol . ' ' . number_format($tunapix_product->price, 2, ',', '.'); ?>
                    </button>
                    
                    <!-- Loading -->
                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <p>Processando pagamento...</p>
                    </div>
                </form>
            </div>
            
            <!-- Coluna Direita - Benefícios -->
            <div class="benefits-column">
                <h2 class="benefits-title">Por que escolher este produto?</h2>
                
                <ul class="benefits-list">
                    <li>✅ Acesso imediato ao conteúdo</li>
                    <li>✅ Suporte especializado</li>
                    <li>✅ Garantia de 30 dias</li>
                    <li>✅ Atualizações gratuitas</li>
                    <li>✅ Comunidade exclusiva</li>
                </ul>
                
                <div class="guarantee-box">
                    <div class="guarantee-title">Garantia de 30 Dias</div>
                    <div class="guarantee-text">
                        Se não ficar satisfeito, devolvemos 100% do seu dinheiro
                    </div>
                </div>
                
                <div class="guarantee-box">
                    <div class="guarantee-title">Pagamento Seguro</div>
                    <div class="guarantee-text">
                        Seus dados estão protegidos com criptografia SSL
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Timer de contagem regressiva
        function startCountdown() {
            const now = new Date().getTime();
            const end = now + (24 * 60 * 60 * 1000); // 24 horas
            
            const timer = setInterval(function() {
                const now = new Date().getTime();
                const distance = end - now;
                
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                
                document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
                
                if (distance < 0) {
                    clearInterval(timer);
                    document.getElementById('countdown').innerHTML = "OFERTA ENCERRADA";
                }
            }, 1000);
        }
        
        // Métodos de pagamento
        document.querySelectorAll('.payment-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.payment-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                document.getElementById('payment_method').value = this.dataset.method;
            });
        });
        
        // Order bump
        document.getElementById('order_bump')?.addEventListener('change', function() {
            const button = document.getElementById('checkout-button');
            const basePrice = <?php echo $tunapix_product->price; ?>;
            const bumpPrice = <?php echo $tunapix_product->order_bump_price ?? 0; ?>;
            const currencySymbol = '<?php echo $currency_symbol; ?>';
            
            if (this.checked) {
                const total = basePrice + bumpPrice;
                button.textContent = `Finalizar Compra - ${currencySymbol} ${total.toFixed(2).replace('.', ',')}`;
            } else {
                button.textContent = `Finalizar Compra - ${currencySymbol} ${basePrice.toFixed(2).replace('.', ',')}`;
            }
        });
        
        // Formulário de checkout
        document.getElementById('checkout-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const button = document.getElementById('checkout-button');
            const loading = document.getElementById('loading');
            const message = document.getElementById('message');
            
            // Mostrar loading
            button.style.display = 'none';
            loading.style.display = 'block';
            message.style.display = 'none';
            
            // Enviar dados
            fetch(tunapix_checkout.ajax_url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    message.className = 'message success';
                    message.textContent = data.data.message;
                    message.style.display = 'block';
                    
                    // Redirecionar após 2 segundos
                    setTimeout(() => {
                        window.location.href = data.data.redirect_url;
                    }, 2000);
                } else {
                    message.className = 'message error';
                    message.textContent = data.data;
                    message.style.display = 'block';
                }
            })
            .catch(error => {
                message.className = 'message error';
                message.textContent = 'Erro ao processar pagamento. Tente novamente.';
                message.style.display = 'block';
            })
            .finally(() => {
                button.style.display = 'block';
                loading.style.display = 'none';
            });
        });
        
        // Iniciar timer
        startCountdown();
    </script>
</body>
</html> 