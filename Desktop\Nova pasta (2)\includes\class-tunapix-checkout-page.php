<?php
/**
 * Classe da Página de Checkout
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Checkout_Page {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'add_rewrite_rules'));
        add_action('template_redirect', array($this, 'load_checkout_template'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_checkout_scripts'));
        add_action('wp_ajax_tunapix_process_payment', array($this, 'process_payment'));
        add_action('wp_ajax_nopriv_tunapix_process_payment', array($this, 'process_payment'));
    }
    
    public function add_rewrite_rules() {
        add_rewrite_rule(
            'checkout/([0-9]+)/?$',
            'index.php?tunapix_checkout=1&product_id=$matches[1]',
            'top'
        );
        
        add_rewrite_tag('%tunapix_checkout%', '([^&]+)');
        add_rewrite_tag('%product_id%', '([0-9]+)');
    }
    
    public function load_checkout_template() {
        if (get_query_var('tunapix_checkout')) {
            $product_id = get_query_var('product_id');
            $product = TunaPix_Products::get_instance()->get_product_by_id($product_id);
            
            if (!$product) {
                wp_redirect(home_url());
                exit;
            }
            
            // Definir variáveis globais para o template
            global $tunapix_product;
            $tunapix_product = $product;
            
            // Carregar template personalizado
            include TUNAPIX_PLUGIN_PATH . 'templates/checkout/checkout.php';
            exit;
        }
    }
    
    public function enqueue_checkout_scripts() {
        if (get_query_var('tunapix_checkout')) {
            wp_enqueue_style('tunapix-checkout', TUNAPIX_PLUGIN_URL . 'assets/css/checkout.css', array(), TUNAPIX_VERSION);
            wp_enqueue_script('tunapix-checkout', TUNAPIX_PLUGIN_URL . 'assets/js/checkout.js', array('jquery'), TUNAPIX_VERSION, true);
            
            wp_localize_script('tunapix-checkout', 'tunapix_checkout', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('tunapix_checkout_nonce'),
                'currency_symbol' => $this->get_currency_symbol(),
                'product' => $this->get_product_data()
            ));
        }
    }
    
    private function get_currency_symbol() {
        $currency = get_option('tunapix_currency', 'BRL');
        $symbols = array(
            'BRL' => 'R$',
            'USD' => '$',
            'EUR' => '€'
        );
        return isset($symbols[$currency]) ? $symbols[$currency] : 'R$';
    }
    
    private function get_product_data() {
        $product_id = get_query_var('product_id');
        $product = TunaPix_Products::get_instance()->get_product_by_id($product_id);
        
        if ($product) {
            return array(
                'id' => $product->id,
                'name' => $product->name,
                'price' => $product->price,
                'currency' => $product->currency,
                'order_bump_enabled' => $product->order_bump_enabled,
                'order_bump_name' => $product->order_bump_name,
                'order_bump_price' => $product->order_bump_price,
                'theme' => $product->theme,
                'custom_colors' => $product->custom_colors
            );
        }
        
        return null;
    }
    
    public function process_payment() {
        check_ajax_referer('tunapix_checkout_nonce', 'nonce');
        
        $product_id = intval($_POST['product_id']);
        $product = TunaPix_Products::get_instance()->get_product_by_id($product_id);
        
        if (!$product) {
            wp_send_json_error('Produto não encontrado');
        }
        
        // Validar dados do cliente
        $customer_data = array(
            'name' => sanitize_text_field($_POST['customer_name']),
            'email' => sanitize_email($_POST['customer_email']),
            'cpf' => sanitize_text_field($_POST['customer_cpf']),
            'phone' => sanitize_text_field($_POST['customer_phone']),
            'payment_method' => sanitize_text_field($_POST['payment_method']),
            'order_bump_purchased' => intval($_POST['order_bump_purchased'])
        );
        
        // Validar campos obrigatórios
        if (empty($customer_data['name']) || empty($customer_data['email'])) {
            wp_send_json_error('Nome e e-mail são obrigatórios');
        }
        
        // Processar pagamento
        $payment_result = TunaPix_Payments::get_instance()->process_payment($product, $customer_data);
        
        if ($payment_result['success']) {
            // Disparar eventos do Facebook Pixel
            TunaPix_Facebook_Pixel::get_instance()->track_purchase($product, $customer_data);
            
            // Enviar webhook
            TunaPix_Webhooks::get_instance()->send_webhook($payment_result['order_id'], 'purchase');
            
            // Enviar e-mail de entrega
            $this->send_delivery_email($product, $customer_data, $payment_result['order_id']);
            
            wp_send_json_success(array(
                'message' => 'Pagamento processado com sucesso!',
                'redirect_url' => $product->redirect_url ?: home_url('/obrigado/'),
                'order_id' => $payment_result['order_id']
            ));
        } else {
            wp_send_json_error($payment_result['message']);
        }
    }
    
    private function send_delivery_email($product, $customer_data, $order_id) {
        $to = $customer_data['email'];
        $subject = 'Acesso ao seu produto - ' . $product->name;
        
        // Template do e-mail principal
        $message = $product->delivery_email;
        
        // Substituir variáveis
        $message = str_replace('{nome}', $customer_data['name'], $message);
        $message = str_replace('{produto}', $product->name, $message);
        $message = str_replace('{pedido}', $order_id, $message);
        
        // Enviar e-mail principal
        wp_mail($to, $subject, $message, array('Content-Type: text/html; charset=UTF-8'));
        
        // Se comprou order bump, enviar e-mail adicional
        if ($customer_data['order_bump_purchased'] && $product->order_bump_delivery_email) {
            $ob_subject = 'Acesso ao seu bônus - ' . $product->order_bump_name;
            $ob_message = $product->order_bump_delivery_email;
            
            $ob_message = str_replace('{nome}', $customer_data['name'], $ob_message);
            $ob_message = str_replace('{produto}', $product->order_bump_name, $ob_message);
            $ob_message = str_replace('{pedido}', $order_id, $ob_message);
            
            wp_mail($to, $ob_subject, $ob_message, array('Content-Type: text/html; charset=UTF-8'));
        }
    }
    
    public function get_checkout_styles($product) {
        $theme = $product->theme;
        $custom_colors = $product->custom_colors;
        
        $styles = '';
        
        if ($theme === 'dark') {
            $styles .= '
                .tunapix-checkout {
                    background: #1a1a1a;
                    color: #ffffff;
                }
                .tunapix-checkout .checkout-form {
                    background: #2d2d2d;
                }
                .tunapix-checkout input, .tunapix-checkout select {
                    background: #3d3d3d;
                    color: #ffffff;
                    border: 1px solid #555;
                }
            ';
        }
        
        if ($custom_colors) {
            if (!empty($custom_colors['primary'])) {
                $styles .= '.tunapix-checkout .btn-primary { background-color: ' . $custom_colors['primary'] . '; }';
            }
            if (!empty($custom_colors['secondary'])) {
                $styles .= '.tunapix-checkout .btn-secondary { background-color: ' . $custom_colors['secondary'] . '; }';
            }
            if (!empty($custom_colors['text'])) {
                $styles .= '.tunapix-checkout { color: ' . $custom_colors['text'] . '; }';
            }
        }
        
        return $styles;
    }
} 