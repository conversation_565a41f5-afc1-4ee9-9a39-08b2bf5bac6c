<?php
/**
 * Classe de Processamento de Pagamentos
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Payments {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Carregar gateways de pagamento
        $this->load_gateways();
    }
    
    private function load_gateways() {
        $gateway_files = array(
            'mercadopago' => 'class-tunapix-mercadopago.php',
            'stripe' => 'class-tunapix-stripe.php',
            'asaas' => 'class-tunapix-asaas.php',
            'pagseguro' => 'class-tunapix-pagseguro.php',
            'pushpay' => 'class-tunapix-pushpay.php',
            'cashtime' => 'class-tunapix-cashtime.php'
        );
        
        foreach ($gateway_files as $gateway => $filename) {
            $file_path = TUNAPIX_PLUGIN_PATH . 'includes/gateways/' . $filename;
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }
    }
    
    public function process_payment($product, $customer_data) {
        global $wpdb;
        
        // Calcular valor total
        $total_amount = $product->price;
        if ($customer_data['order_bump_purchased']) {
            $total_amount += $product->order_bump_price;
        }
        
        // Criar pedido no banco de dados
        $order_data = array(
            'product_id' => $product->id,
            'customer_name' => $customer_data['name'],
            'customer_email' => $customer_data['email'],
            'customer_cpf' => $customer_data['cpf'],
            'customer_phone' => $customer_data['phone'],
            'payment_method' => $customer_data['payment_method'],
            'payment_status' => 'pending',
            'amount' => $total_amount,
            'currency' => $product->currency,
            'order_bump_purchased' => $customer_data['order_bump_purchased'],
            'order_bump_amount' => $customer_data['order_bump_purchased'] ? $product->order_bump_price : 0,
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        );
        
        $result = $wpdb->insert(
            $wpdb->prefix . 'tunapix_orders',
            $order_data,
            array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%f', '%s', '%d', '%f', '%s', '%s')
        );
        
        if (!$result) {
            return array('success' => false, 'message' => 'Erro ao criar pedido');
        }
        
        $order_id = $wpdb->insert_id;
        
        // Processar pagamento com gateway selecionado
        $gateway = $this->get_gateway_instance($customer_data['payment_method']);
        
        if (!$gateway) {
            return array('success' => false, 'message' => 'Gateway de pagamento não encontrado');
        }
        
        $payment_result = $gateway->process_payment($order_id, $product, $customer_data);
        
        if ($payment_result['success']) {
            // Atualizar status do pedido
            $wpdb->update(
                $wpdb->prefix . 'tunapix_orders',
                array(
                    'payment_status' => 'completed',
                    'payment_gateway' => $customer_data['payment_method'],
                    'gateway_transaction_id' => $payment_result['transaction_id']
                ),
                array('id' => $order_id),
                array('%s', '%s', '%s'),
                array('%d')
            );
            
            return array(
                'success' => true,
                'order_id' => $order_id,
                'transaction_id' => $payment_result['transaction_id']
            );
        } else {
            // Atualizar status do pedido para falha
            $wpdb->update(
                $wpdb->prefix . 'tunapix_orders',
                array(
                    'payment_status' => 'failed',
                    'payment_gateway' => $customer_data['payment_method']
                ),
                array('id' => $order_id),
                array('%s', '%s'),
                array('%d')
            );
            
            return array('success' => false, 'message' => $payment_result['message']);
        }
    }
    
    private function get_gateway_instance($gateway_name) {
        $gateway_class = 'TunaPix_' . ucfirst($gateway_name);
        
        if (class_exists($gateway_class)) {
            return new $gateway_class();
        }
        
        return null;
    }
    
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '';
    }
    
    public function get_available_gateways() {
        $enabled_gateways = get_option('tunapix_payment_gateways', array());
        $available_gateways = array();
        
        $gateways = array(
            'mercadopago' => array(
                'name' => 'Mercado Pago',
                'icon' => 'mercadopago-icon.png',
                'description' => 'Aceita cartões, PIX e boleto'
            ),
            'stripe' => array(
                'name' => 'Stripe',
                'icon' => 'stripe-icon.png',
                'description' => 'Cartões de crédito e débito'
            ),
            'asaas' => array(
                'name' => 'Asaas',
                'icon' => 'asaas-icon.png',
                'description' => 'PIX, boleto e cartão'
            ),
            'pagseguro' => array(
                'name' => 'PagSeguro',
                'icon' => 'pagseguro-icon.png',
                'description' => 'Múltiplas formas de pagamento'
            ),
            'pushpay' => array(
                'name' => 'PushPay',
                'icon' => 'pushpay-icon.png',
                'description' => 'Pagamentos móveis'
            ),
            'cashtime' => array(
                'name' => 'CashTime',
                'icon' => 'cashtime-icon.png',
                'description' => 'Pagamentos instantâneos'
            )
        );
        
        foreach ($gateways as $key => $gateway) {
            if (isset($enabled_gateways[$key]) && $enabled_gateways[$key]) {
                $available_gateways[$key] = $gateway;
            }
        }
        
        return $available_gateways;
    }
} 