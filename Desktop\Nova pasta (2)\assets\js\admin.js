/**
 * TunaPix Admin JavaScript
 */

jQuery(document).ready(function($) {
    
    // Inicializar componentes
    initTabs();
    initToggles();
    initColorPickers();
    initFileUploads();
    initModals();
    initTooltips();
    
    // Função para inicializar tabs
    function initTabs() {
        $('.tunapix-tab').on('click', function(e) {
            e.preventDefault();
            
            const target = $(this).data('target');
            
            // Remover classe active de todas as tabs
            $('.tunapix-tab').removeClass('active');
            $('.tunapix-tab-content').removeClass('active');
            
            // Adicionar classe active na tab clicada
            $(this).addClass('active');
            $('#' + target).addClass('active');
        });
    }
    
    // Função para inicializar toggles
    function initToggles() {
        $('.tunapix-toggle input').on('change', function() {
            const isChecked = $(this).is(':checked');
            const fieldName = $(this).data('field');
            
            // Atualizar visual do toggle
            if (isChecked) {
                $(this).siblings('.tunapix-slider').addClass('active');
            } else {
                $(this).siblings('.tunapix-slider').removeClass('active');
            }
            
            // Salvar configuração via AJAX
            saveToggleSetting(fieldName, isChecked);
        });
    }
    
    // Função para inicializar color pickers
    function initColorPickers() {
        $('.tunapix-color-picker input[type="color"]').on('change', function() {
            const color = $(this).val();
            const fieldName = $(this).data('field');
            
            // Atualizar preview
            updateColorPreview(fieldName, color);
            
            // Salvar configuração
            saveColorSetting(fieldName, color);
        });
    }
    
    // Função para inicializar file uploads
    function initFileUploads() {
        $('.tunapix-file-upload').on('click', function() {
            $(this).find('input[type="file"]').click();
        });
        
        $('.tunapix-file-upload input[type="file"]').on('change', function() {
            const file = this.files[0];
            const container = $(this).closest('.tunapix-file-upload');
            
            if (file) {
                // Mostrar preview
                showFilePreview(container, file);
                
                // Upload do arquivo
                uploadFile(file, container);
            }
        });
    }
    
    // Função para inicializar modais
    function initModals() {
        // Abrir modal
        $('.tunapix-modal-trigger').on('click', function(e) {
            e.preventDefault();
            const modalId = $(this).data('modal');
            $('#' + modalId).show();
        });
        
        // Fechar modal
        $('.tunapix-modal-close').on('click', function() {
            $(this).closest('.tunapix-modal').hide();
        });
        
        // Fechar modal clicando fora
        $('.tunapix-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        });
    }
    
    // Função para inicializar tooltips
    function initTooltips() {
        $('.tunapix-tooltip').on('mouseenter', function() {
            const tooltip = $(this).find('.tunapix-tooltiptext');
            tooltip.show();
        }).on('mouseleave', function() {
            const tooltip = $(this).find('.tunapix-tooltiptext');
            tooltip.hide();
        });
    }
    
    // Função para salvar configuração de toggle
    function saveToggleSetting(fieldName, value) {
        $.ajax({
            url: tunapix_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'tunapix_save_setting',
                nonce: tunapix_ajax.nonce,
                field: fieldName,
                value: value
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Configuração salva com sucesso!', 'success');
                } else {
                    showNotification('Erro ao salvar configuração', 'error');
                }
            },
            error: function() {
                showNotification('Erro ao salvar configuração', 'error');
            }
        });
    }
    
    // Função para salvar configuração de cor
    function saveColorSetting(fieldName, color) {
        $.ajax({
            url: tunapix_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'tunapix_save_setting',
                nonce: tunapix_ajax.nonce,
                field: fieldName,
                value: color
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Cor salva com sucesso!', 'success');
                } else {
                    showNotification('Erro ao salvar cor', 'error');
                }
            },
            error: function() {
                showNotification('Erro ao salvar cor', 'error');
            }
        });
    }
    
    // Função para atualizar preview de cor
    function updateColorPreview(fieldName, color) {
        $('[data-preview="' + fieldName + '"]').css('background-color', color);
    }
    
    // Função para mostrar preview de arquivo
    function showFilePreview(container, file) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            const preview = $('<div class="file-preview"><img src="' + e.target.result + '" style="max-width: 200px; max-height: 200px;"></div>');
            container.find('.file-preview').remove();
            container.append(preview);
        };
        
        if (file.type.startsWith('image/')) {
            reader.readAsDataURL(file);
        }
    }
    
    // Função para upload de arquivo
    function uploadFile(file, container) {
        const formData = new FormData();
        formData.append('action', 'tunapix_upload_file');
        formData.append('nonce', tunapix_ajax.nonce);
        formData.append('file', file);
        
        $.ajax({
            url: tunapix_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    container.find('input[type="hidden"]').val(response.data.url);
                    showNotification('Arquivo enviado com sucesso!', 'success');
                } else {
                    showNotification('Erro ao enviar arquivo', 'error');
                }
            },
            error: function() {
                showNotification('Erro ao enviar arquivo', 'error');
            }
        });
    }
    
    // Função para mostrar notificações
    function showNotification(message, type) {
        const notification = $('<div class="tunapix-notification tunapix-notification-' + type + '">' + message + '</div>');
        
        $('body').append(notification);
        
        // Mostrar notificação
        setTimeout(function() {
            notification.addClass('show');
        }, 100);
        
        // Remover notificação após 3 segundos
        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 3000);
    }
    
    // Função para copiar texto para clipboard
    function copyToClipboard(text) {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
        
        showNotification('Texto copiado para a área de transferência!', 'success');
    }
    
    // Event listeners para botões de copiar
    $(document).on('click', '.copy-button', function() {
        const text = $(this).data('copy');
        copyToClipboard(text);
    });
    
    // Função para confirmar exclusão
    function confirmDelete(message) {
        return confirm(message || 'Tem certeza que deseja excluir este item?');
    }
    
    // Event listeners para botões de exclusão
    $(document).on('click', '.delete-button', function(e) {
        if (!confirmDelete()) {
            e.preventDefault();
            return false;
        }
    });
    
    // Função para validar formulários
    function validateForm(form) {
        let isValid = true;
        
        form.find('[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('error');
                isValid = false;
            } else {
                $(this).removeClass('error');
            }
        });
        
        return isValid;
    }
    
    // Event listeners para formulários
    $('.tunapix-form').on('submit', function(e) {
        if (!validateForm($(this))) {
            e.preventDefault();
            showNotification('Por favor, preencha todos os campos obrigatórios', 'error');
            return false;
        }
    });
    
    // Função para carregar dados via AJAX
    function loadData(action, data, callback) {
        $.ajax({
            url: tunapix_ajax.ajax_url,
            type: 'POST',
            data: {
                action: action,
                nonce: tunapix_ajax.nonce,
                ...data
            },
            success: function(response) {
                if (response.success) {
                    callback(response.data);
                } else {
                    showNotification(response.data || 'Erro ao carregar dados', 'error');
                }
            },
            error: function() {
                showNotification('Erro ao carregar dados', 'error');
            }
        });
    }
    
    // Função para salvar dados via AJAX
    function saveData(action, data, callback) {
        $.ajax({
            url: tunapix_ajax.ajax_url,
            type: 'POST',
            data: {
                action: action,
                nonce: tunapix_ajax.nonce,
                ...data
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Dados salvos com sucesso!', 'success');
                    if (callback) callback(response.data);
                } else {
                    showNotification(response.data || 'Erro ao salvar dados', 'error');
                }
            },
            error: function() {
                showNotification('Erro ao salvar dados', 'error');
            }
        });
    }
    
    // Exportar funções para uso global
    window.TunaPixAdmin = {
        showNotification: showNotification,
        loadData: loadData,
        saveData: saveData,
        copyToClipboard: copyToClipboard,
        confirmDelete: confirmDelete
    };
    
});

// Estilos para notificações
const notificationStyles = `
<style>
.tunapix-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.tunapix-notification.show {
    transform: translateX(0);
}

.tunapix-notification-success {
    background: #28a745;
}

.tunapix-notification-error {
    background: #dc3545;
}

.tunapix-notification-warning {
    background: #ffc107;
    color: #333;
}

.tunapix-notification-info {
    background: #17a2b8;
}

.tunapix-form input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.file-preview {
    margin-top: 10px;
    text-align: center;
}

.file-preview img {
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
`;

// Adicionar estilos ao head
document.head.insertAdjacentHTML('beforeend', notificationStyles); 