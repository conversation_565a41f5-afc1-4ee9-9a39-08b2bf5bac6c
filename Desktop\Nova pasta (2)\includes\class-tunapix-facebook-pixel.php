<?php
/**
 * Classe de Integração com Facebook Pixel
 */

if (!defined('ABSPATH')) {
    exit;
}

class TunaPix_Facebook_Pixel {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_head', array($this, 'add_pixel_code'));
        add_action('wp_footer', array($this, 'add_pixel_events'));
    }
    
    public function add_pixel_code() {
        $pixels = $this->get_pixels();
        
        if (empty($pixels)) {
            return;
        }
        
        echo '<script>' . "\n";
        echo '!function(f,b,e,v,n,t,s)' . "\n";
        echo '{if(f.fbq)return;n=f.fbq=function(){n.callMethod?' . "\n";
        echo 'n.callMethod.apply(n,arguments):n.queue.push(arguments)};' . "\n";
        echo 'if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version=\'2.0\';' . "\n";
        echo 'n.queue=[];t=b.createElement(e);t.async=!0;' . "\n";
        echo 't.src=v;s=b.getElementsByTagName(e)[0];' . "\n";
        echo 's.parentNode.insertBefore(t,s)}(window, document,\'script\',\'https://connect.facebook.net/en_US/fbevents.js\');' . "\n";
        
        foreach ($pixels as $pixel_id) {
            echo 'fbq(\'init\', \'' . esc_js($pixel_id) . '\');' . "\n";
        }
        
        echo 'fbq(\'track\', \'PageView\');' . "\n";
        echo '</script>' . "\n";
        
        // Adicionar noscript tags
        echo '<noscript>' . "\n";
        foreach ($pixels as $pixel_id) {
            echo '<img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=' . esc_attr($pixel_id) . '&ev=PageView&noscript=1"/>' . "\n";
        }
        echo '</noscript>' . "\n";
    }
    
    public function add_pixel_events() {
        if (get_query_var('tunapix_checkout')) {
            $product_id = get_query_var('product_id');
            $product = TunaPix_Products::get_instance()->get_product_by_id($product_id);
            
            if ($product) {
                echo '<script>' . "\n";
                echo 'fbq(\'track\', \'InitiateCheckout\', {' . "\n";
                echo '    content_name: \'' . esc_js($product->name) . '\',' . "\n";
                echo '    content_category: \'product\',' . "\n";
                echo '    value: ' . floatval($product->price) . ',' . "\n";
                echo '    currency: \'' . esc_js($product->currency) . '\'' . "\n";
                echo '});' . "\n";
                echo '</script>' . "\n";
            }
        }
    }
    
    public function track_purchase($product, $customer_data) {
        $pixels = $this->get_pixels();
        
        if (empty($pixels)) {
            return;
        }
        
        $total_value = $product->price;
        if ($customer_data['order_bump_purchased']) {
            $total_value += $product->order_bump_price;
        }
        
        $pixel_data = array(
            'content_name' => $product->name,
            'content_category' => 'product',
            'value' => $total_value,
            'currency' => $product->currency,
            'content_ids' => array($product->id),
            'content_type' => 'product'
        );
        
        // Enviar evento via AJAX para todos os pixels
        foreach ($pixels as $pixel_id) {
            $this->send_pixel_event($pixel_id, 'Purchase', $pixel_data);
        }
    }
    
    private function send_pixel_event($pixel_id, $event_name, $event_data) {
        $url = 'https://graph.facebook.com/v17.0/' . $pixel_id . '/events';
        
        $data = array(
            'data' => array(
                array(
                    'event_name' => $event_name,
                    'event_time' => time(),
                    'action_source' => 'website',
                    'event_source_url' => home_url('/checkout/' . $event_data['content_ids'][0]),
                    'user_data' => array(
                        'em' => hash('sha256', strtolower($event_data['customer_email'] ?? '')),
                        'ph' => hash('sha256', preg_replace('/[^0-9]/', '', $event_data['customer_phone'] ?? ''))
                    ),
                    'custom_data' => array(
                        'content_name' => $event_data['content_name'],
                        'content_category' => $event_data['content_category'],
                        'value' => $event_data['value'],
                        'currency' => $event_data['currency'],
                        'content_ids' => $event_data['content_ids'],
                        'content_type' => $event_data['content_type']
                    )
                )
            ),
            'access_token' => get_option('tunapix_facebook_access_token', '')
        );
        
        $response = wp_remote_post($url, array(
            'headers' => array(
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            error_log('TunaPix: Erro ao enviar evento do Facebook Pixel: ' . $response->get_error_message());
        }
    }
    
    private function get_pixels() {
        $pixels = array();
        
        // Pixels globais
        $global_pixels = get_option('tunapix_facebook_pixels', '');
        if ($global_pixels) {
            $pixels = array_merge($pixels, $this->parse_pixel_ids($global_pixels));
        }
        
        // Pixels específicos do produto (se estiver na página de checkout)
        if (get_query_var('tunapix_checkout')) {
            $product_id = get_query_var('product_id');
            $product = TunaPix_Products::get_instance()->get_product_by_id($product_id);
            
            if ($product && $product->facebook_pixels) {
                $product_pixels = $this->parse_pixel_ids($product->facebook_pixels);
                $pixels = array_merge($pixels, $product_pixels);
            }
        }
        
        return array_unique($pixels);
    }
    
    private function parse_pixel_ids($pixel_string) {
        $pixels = array();
        $lines = explode("\n", $pixel_string);
        
        foreach ($lines as $line) {
            $pixel_id = trim($line);
            if (!empty($pixel_id) && is_numeric($pixel_id)) {
                $pixels[] = $pixel_id;
            }
        }
        
        return $pixels;
    }
    
    public function track_view_content($product) {
        $pixels = $this->get_pixels();
        
        if (empty($pixels)) {
            return;
        }
        
        $pixel_data = array(
            'content_name' => $product->name,
            'content_category' => 'product',
            'value' => $product->price,
            'currency' => $product->currency,
            'content_ids' => array($product->id),
            'content_type' => 'product'
        );
        
        foreach ($pixels as $pixel_id) {
            $this->send_pixel_event($pixel_id, 'ViewContent', $pixel_data);
        }
    }
    
    public function track_add_to_cart($product) {
        $pixels = $this->get_pixels();
        
        if (empty($pixels)) {
            return;
        }
        
        $pixel_data = array(
            'content_name' => $product->name,
            'content_category' => 'product',
            'value' => $product->price,
            'currency' => $product->currency,
            'content_ids' => array($product->id),
            'content_type' => 'product'
        );
        
        foreach ($pixels as $pixel_id) {
            $this->send_pixel_event($pixel_id, 'AddToCart', $pixel_data);
        }
    }
} 